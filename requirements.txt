# Core web framework
flask==3.0.0
flask-cors==4.0.0
gunicorn==21.2.0

# Data analysis and manipulation
pandas==2.2.0
numpy==1.26.2
scipy>=1.9.0
pyarrow==14.0.0

# Financial data
yfinance==0.2.63
yahooquery==2.4.1
exchange-calendars==4.10.1

# Machine learning and optimization
torch==2.7.0
torchvision==0.22.0
scikit-learn==1.3.2
optuna==3.3.0
prophet==1.1.7

# Technical analysis
pandas_ta==0.3.14b0

# Visualization
matplotlib==3.8.0
seaborn==0.13.0
plotnine==0.14.5
mizani>=0.13.0

# Database
SQLAlchemy==2.0.23

# Utilities
python-dotenv==1.0.0
requests==2.31.0
tenacity>=8.0.1
loguru==0.7.3
schedule==1.2.2

# AI/LLM
openai==1.96.1

# Testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-xdist
pytest-mock

# Code quality
flake8
black
isort
safety
bandit