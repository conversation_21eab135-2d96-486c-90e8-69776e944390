name: Dependency Updates

on:
  schedule:
    # Run every Monday at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch:

jobs:
  update-python-dependencies:
    name: Update Python Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install pip-tools
        run: |
          python -m pip install --upgrade pip
          pip install pip-tools

      - name: Update requirements
        working-directory: ./backend
        run: |
          # Update requirements.in if it exists, otherwise use requirements.txt
          if [ -f requirements.in ]; then
            pip-compile --upgrade requirements.in
          else
            # Create requirements.in from current requirements.txt
            cp requirements.txt requirements.in
            pip-compile --upgrade requirements.in
          fi

      - name: Test updated dependencies
        working-directory: ./backend
        run: |
          curl -LsSf https://github.com/astral-sh/uv/releases/latest/download/uv-installer.sh | sh
          uv pip install --upgrade pip
          uv pip install -r requirements.txt
          python -m pytest tests/ --maxfail=5 -x

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update Python dependencies'
          title: '🔄 Update Python Dependencies'
          body: |
            ## Python Dependencies Update
            
            This PR updates Python dependencies to their latest compatible versions.
            
            ### Changes
            - Updated `requirements.txt` with latest package versions
            - All tests are passing with the new dependencies
            
            ### Testing
            - [x] Unit tests pass
            - [x] Dependencies install successfully
            
            **Auto-generated by GitHub Actions**
          branch: update/python-dependencies
          delete-branch: true
          labels: |
            dependencies
            python
            automated

  update-node-dependencies:
    name: Update Node.js Dependencies
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Update dependencies
        working-directory: ./frontend
        run: |
          # Update all dependencies to latest
          npx npm-check-updates -u
          npm install

      - name: Run tests
        working-directory: ./frontend
        run: |
          npm run test
          npm run build

      - name: Create Pull Request
        uses: peter-evans/create-pull-request@v5
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: 'chore: update Node.js dependencies'
          title: '🔄 Update Node.js Dependencies'
          body: |
            ## Node.js Dependencies Update
            
            This PR updates Node.js dependencies to their latest compatible versions.
            
            ### Changes
            - Updated `package.json` with latest package versions
            - Updated `package-lock.json`
            - All tests are passing with the new dependencies
            
            ### Testing
            - [x] Unit tests pass
            - [x] Build succeeds
            - [x] Dependencies install successfully
            
            **Auto-generated by GitHub Actions**
          branch: update/node-dependencies
          delete-branch: true
          labels: |
            dependencies
            nodejs
            automated

  security-audit:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python 3.12
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Python security audit
        working-directory: ./backend
        run: |
          pip install safety
          safety check --json --output safety-report.json || true
          safety check

      - name: Set up Node.js 18
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json

      - name: Node.js security audit
        working-directory: ./frontend
        run: |
          npm audit --audit-level=moderate

      - name: Upload security reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-audit-reports
          path: |
            backend/safety-report.json
          retention-days: 30

      - name: Create security issue
        if: failure()
        uses: actions/github-script@v7
        with:
          script: |
            const title = '🚨 Security Vulnerabilities Detected';
            const body = `
            ## Security Audit Failed
            
            Security vulnerabilities have been detected in the project dependencies.
            
            ### Action Required
            - Review the security audit reports in the workflow artifacts
            - Update vulnerable dependencies
            - Run security tests locally before pushing fixes
            
            ### Reports
            - Python: Check safety-report.json in workflow artifacts
            - Node.js: Check npm audit output in workflow logs
            
            **Auto-generated by GitHub Actions**
            `;
            
            // Check if issue already exists
            const issues = await github.rest.issues.listForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              state: 'open',
              labels: 'security,automated'
            });
            
            const existingIssue = issues.data.find(issue => issue.title === title);
            
            if (!existingIssue) {
              await github.rest.issues.create({
                owner: context.repo.owner,
                repo: context.repo.repo,
                title: title,
                body: body,
                labels: ['security', 'automated', 'high-priority']
              });
            }

  cleanup-old-artifacts:
    name: Cleanup Old Artifacts
    runs-on: ubuntu-latest
    
    steps:
      - name: Delete old artifacts
        uses: actions/github-script@v7
        with:
          script: |
            const artifacts = await github.rest.actions.listArtifactsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
              per_page: 100
            });
            
            const cutoffDate = new Date();
            cutoffDate.setDate(cutoffDate.getDate() - 30); // Keep artifacts for 30 days
            
            for (const artifact of artifacts.data.artifacts) {
              const createdAt = new Date(artifact.created_at);
              if (createdAt < cutoffDate) {
                console.log(`Deleting artifact: ${artifact.name} (${artifact.created_at})`);
                await github.rest.actions.deleteArtifact({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  artifact_id: artifact.id
                });
              }
            }
