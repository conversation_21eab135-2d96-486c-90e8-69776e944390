// 日志级别
const LOG_LEVELS = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error',
};

// 获取当前时间戳
const getTimestamp = () => {
  return new Date().toISOString();
};

// 格式化日志消息
const formatMessage = (level, component, message, data = null) => {
  const timestamp = getTimestamp();
  const logData = {
    timestamp,
    level,
    component,
    message,
    ...(data && { data }),
  };
  return logData;
};

// 控制台输出样式
const CONSOLE_STYLES = {
  [LOG_LEVELS.DEBUG]: 'color: #6c757d',
  [LOG_LEVELS.INFO]: 'color: #0d6efd',
  [LOG_LEVELS.WARN]: 'color: #ffc107',
  [LOG_LEVELS.ERROR]: 'color: #dc3545; font-weight: bold',
};

// 日志处理函数
const logToConsole = logData => {
  const { level, component, message, timestamp, data } = logData;
  const style = CONSOLE_STYLES[level];

  console.groupCollapsed(
    `%c[${timestamp}] [${level.toUpperCase()}] [${component}]`,
    style
  );
  console.log('Message:', message);
  if (data) {
    console.log('Data:', data);
  }
  console.groupEnd();
};

// 创建logger实例
class Logger {
  constructor(component) {
    this.component = component;
  }

  _log(level, message, data = null) {
    const logData = formatMessage(level, this.component, message, data);
    logToConsole(logData);

    // 如果是错误级别，还可以发送到错误跟踪服务
    if (level === LOG_LEVELS.ERROR) {
      // TODO: 集成错误跟踪服务，如 Sentry
    }
  }

  debug(message, data = null) {
    this._log(LOG_LEVELS.DEBUG, message, data);
  }

  info(message, data = null) {
    this._log(LOG_LEVELS.INFO, message, data);
  }

  warn(message, data = null) {
    this._log(LOG_LEVELS.WARN, message, data);
  }

  error(message, data = null) {
    this._log(LOG_LEVELS.ERROR, message, data);
  }
}

export const createLogger = component => new Logger(component);
