import React, { useState, useEffect } from 'react';
import {
  Container,
  Box,
  Grid,
  CardContent,
  CardHeader,
  Tabs,
  Tab,
} from '@mui/material';
import { TabContext, TabPanel } from '@mui/lab';
import { styled } from '@mui/material/styles';
import NavBar from '../components/layout/NavBar';
import FearAndGreedSection from '../components/common/FearAndGreedSection';
import TradingViewStockHeatmap from '../components/charts/TradingViewStockHeatmap';
import TradingViewEtfHeatmap from '../components/charts/TradingViewEtfHeatmap';
import TradingViewNews from '../components/charts/TradingViewNews';
import { GradientTypography, GlassCard } from '../styles/common';
import { get } from '../services/api';

// 美化的容器
const PageContainer = styled(Box)(({ theme }) => ({
  minHeight: 'calc(100vh - 64px)',
  background:
    'linear-gradient(135deg, rgba(248,250,252,0.8) 0%, rgba(255,255,255,0.9) 100%)',
  paddingTop: '32px',
  paddingBottom: '32px',
}));

// 美化的Tab组件
const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-root': {
    marginBottom: '24px',
  },
  '& .MuiTab-root': {
    textTransform: 'none',
    fontWeight: '600',
    fontSize: '1rem',
    color: '#64748B',
    borderRadius: '12px 12px 0 0',
    marginRight: '8px',
    minHeight: '48px',
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      color: '#1976D2',
      background: 'rgba(25, 118, 210, 0.04)',
    },
    '&.Mui-selected': {
      color: '#1976D2',
      background:
        'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%)',
      fontWeight: '700',
    },
  },
  '& .MuiTabs-indicator': {
    height: '3px',
    borderRadius: '3px',
    background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
  },
}));

// 美化的TabPanel
const StyledTabPanel = styled(TabPanel)(({ theme }) => ({
  padding: 0,
  '& > *': {
    animation: 'fadeInUp 0.5s ease-out',
    '@keyframes fadeInUp': {
      '0%': {
        opacity: 0,
        transform: 'translateY(20px)',
      },
      '100%': {
        opacity: 1,
        transform: 'translateY(0)',
      },
    },
  },
}));

// 美化的卡片
const ModernCard = styled(GlassCard)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
  },
}));

// 美化的卡片标题
const StyledCardHeader = styled(CardHeader)(({ theme }) => ({
  background:
    'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%)',
  borderBottom: '1px solid rgba(25, 118, 210, 0.1)',
  '& .MuiCardHeader-title': {
    fontWeight: '700',
    color: '#1976D2',
    fontSize: '1.1rem',
  },
}));

const ReferencePage = () => {
  const [fearGreedData, setFearGreedData] = useState(null);
  const [fearGreedLoading, setFearGreedLoading] = useState(true);
  const [fearGreedError, setFearGreedError] = useState(null);
  const [tabValue, setTabValue] = useState('1');

  const handleTabChange = (event, newValue) => {
    setTabValue(newValue);
  };

  useEffect(() => {
    const fetchFearGreedData = async () => {
      try {
        setFearGreedLoading(true);
        const data = await get('/api/market/fear-greed-index');
        setFearGreedData(data);
        setFearGreedError(null);
      } catch (error) {
        setFearGreedError(error.message || '获取恐惧贪婪指数失败');
      } finally {
        setFearGreedLoading(false);
      }
    };

    fetchFearGreedData();
  }, []);

  return (
    <>
      <NavBar />
      <PageContainer>
        <Container maxWidth="lg">
          <GradientTypography
            variant="h4"
            gutterBottom
            sx={{
              mb: 4,
              fontSize: '2.2rem',
              textAlign: 'center',
              animation: 'fadeInDown 0.6s ease-out',
              '@keyframes fadeInDown': {
                '0%': {
                  opacity: 0,
                  transform: 'translateY(-20px)',
                },
                '100%': {
                  opacity: 1,
                  transform: 'translateY(0)',
                },
              },
            }}
          >
            市场信息
          </GradientTypography>
          <TabContext value={tabValue}>
            <Box
              sx={{
                borderBottom: '1px solid rgba(25, 118, 210, 0.1)',
                mb: 3,
                background:
                  'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%)',
                borderRadius: '16px 16px 0 0',
                backdropFilter: 'blur(8px)',
                padding: '8px 16px',
                boxShadow: '0 2px 8px rgba(0,0,0,0.06)',
              }}
            >
              <StyledTabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="市场信息选项卡"
              >
                <Tab label="情绪" value="1" />
                <Tab label="动向" value="2" />
              </StyledTabs>
            </Box>
            <StyledTabPanel value="1">
              <Grid container spacing={4}>
                <Grid item xs={12} md={4}>
                  <FearAndGreedSection
                    fearGreedData={fearGreedData}
                    fearGreedLoading={fearGreedLoading}
                    fearGreedError={fearGreedError}
                    sx={{ height: '100%' }}
                  />
                </Grid>
                <Grid item xs={12} md={8}>
                  <ModernCard>
                    <StyledCardHeader title="市场新闻" />
                    <CardContent sx={{ flexGrow: 1, padding: '20px' }}>
                      <TradingViewNews />
                    </CardContent>
                  </ModernCard>
                </Grid>
              </Grid>
            </StyledTabPanel>
            <StyledTabPanel value="2">
              <Grid container spacing={4}>
                <Grid item xs={12} md={6}>
                  <ModernCard>
                    <StyledCardHeader title="SPX500 热力图" />
                    <CardContent sx={{ flexGrow: 1, padding: '20px' }}>
                      <TradingViewStockHeatmap />
                    </CardContent>
                  </ModernCard>
                </Grid>
                <Grid item xs={12} md={6}>
                  <ModernCard>
                    <StyledCardHeader title="ETF 热力图" />
                    <CardContent sx={{ flexGrow: 1, padding: '20px' }}>
                      <TradingViewEtfHeatmap />
                    </CardContent>
                  </ModernCard>
                </Grid>
              </Grid>
            </StyledTabPanel>
          </TabContext>
        </Container>
      </PageContainer>
    </>
  );
};

export default ReferencePage;
