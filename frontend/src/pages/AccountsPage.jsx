import { useState } from 'react';
import { Container, Box } from '@mui/material';
import AllAccountSummary from '../components/common/AllAccountSummary';
import NavBar from '../components/layout/NavBar';
import { GradientTypography } from '../styles/common';

const AccountsPage = () => {
  const [refreshKey, setRefreshKey] = useState(0);

  const handleRefresh = () => {
    console.log('AccountsPage: 触发刷新');
    setRefreshKey(prev => prev + 1);
  };

  return (
    <>
      <NavBar />
      <Box sx={{ py: 4 }}>
        <Container maxWidth="lg">
          <GradientTypography
            variant="h4"
            gutterBottom
            sx={{
              mb: 4,
              fontSize: '2.2rem',
              textAlign: 'center',
              animation: 'fadeInDown 0.6s ease-out',
              '@keyframes fadeInDown': {
                '0%': {
                  opacity: 0,
                  transform: 'translateY(-20px)',
                },
                '100%': {
                  opacity: 1,
                  transform: 'translateY(0)',
                },
              },
            }}
          >
            账户管理
          </GradientTypography>
          <AllAccountSummary onRefresh={handleRefresh} key={refreshKey} />
        </Container>
      </Box>
    </>
  );
};

export default AccountsPage;
