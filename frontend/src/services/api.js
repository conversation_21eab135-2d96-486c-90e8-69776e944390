import { API_BASE_URL } from '../constants/config';

const defaultHeaders = {
  'Content-Type': 'application/json',
  Accept: 'application/json',
};

/**
 * 处理 API 错误
 * @param {Response} response - fetch 响应对象
 * @param {Object} data - 响应数据
 * @returns {Error} - 格式化的错误对象
 */
const handleApiError = (response, data) => {
  const status = response.status;
  const statusText = response.statusText;

  // 处理常见的 HTTP 错误
  switch (status) {
    case 400:
      return new Error(data.error || '请求参数错误');
    case 401:
      return new Error(data.error || '未授权的请求');
    case 403:
      return new Error(data.error || '禁止访问');
    case 404:
      return new Error(data.error || '请求的资源不存在');
    case 500:
      return new Error(data.error || '服务器内部错误');
    default:
      return new Error(data.error || `请求失败 (${status}: ${statusText})`);
  }
};

/**
 * 通用 API 请求函数
 * @param {string} endpoint - API 端点
 * @param {Object} options - 请求选项
 * @returns {Promise} - 请求响应
 */
export async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE_URL}${endpoint}`;
  const requestOptions = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
    credentials: 'include', // 允许跨域请求携带凭证
  };

  try {
    const response = await fetch(url, requestOptions);

    // 检查响应的内容类型
    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      throw new Error(`服务器返回了非JSON响应: ${contentType}`);
    }

    const data = await response.json();

    // 处理非 2xx 响应
    if (!response.ok) {
      throw handleApiError(response, data);
    }

    return data;
  } catch (error) {
    console.error('API request failed:', error);
    // 如果是我们自定义的错误，直接抛出
    if (error.message) {
      throw error;
    }
    // 否则抛出一个通用错误
    throw new Error('请求失败，请稍后重试');
  }
}

// GET 请求
export const get = endpoint => apiRequest(endpoint);

// POST 请求
export const post = (endpoint, data) =>
  apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });

// PUT 请求
export const put = (endpoint, data) =>
  apiRequest(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });

// DELETE 请求
export const del = endpoint =>
  apiRequest(endpoint, {
    method: 'DELETE',
  });
