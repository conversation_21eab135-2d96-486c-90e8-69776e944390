// 性能指标中英文映射
export const PERFORMANCE_METRIC_NAMES = {
  annualized_return: '年化收益率',
  sharpe_ratio: '夏普比率',
  win_rate: '胜率',
  profit_loss_ratio: '盈亏比',
  max_gain_info: '最大收益',
  max_loss_info: '最大回撤',
  avg_holding_days: '平均持仓天数',
};

// 风险指标中英文映射
export const RISK_METRIC_NAMES = {
  portfolio_var: '组合方差',
  portfolio_vol: '组合波动率',
  max_drawdown: '最大回撤',
  sharpe_ratio: '夏普比率',
  risk_level: '风险等级',
  concentration_risk: '集中度风险',
};

// 风险指标说明
export const RISK_METRIC_DESCRIPTIONS = {
  portfolio_var:
    '组合方差\n计算方法：基于各资产收益率的协方差矩阵计算\n重要性：反映组合整体波动程度，是风险管理的基础指标',
  portfolio_vol:
    '组合波动率\n计算方法：组合方差的平方根，并年化处理（×√252）\n重要性：直观反映组合风险水平，是最常用的风险度量',
  max_drawdown:
    '最大回撤\n计算方法：(历史最高点 - 当前价值) / 历史最高点\n重要性：衡量最差情况下的潜在损失',
  sharpe_ratio:
    '夏普比率\n计算方法：(年化收益率 - 无风险利率) / 年化波动率\n重要性：衡量每承担一单位风险获得的超额收益',
  risk_level:
    '风险等级\n计算方法：基于组合波动率划分\nHIGH: >25%（高风险）\nMEDIUM: 15-25%（中等风险）\nLOW: <15%（低风险）',
  concentration_risk:
    '集中度风险\n计算方法：单一持仓市值/总组合市值的最大值\n重要性：衡量组合的分散程度，避免过度集中于单一资产\n参考标准：<20%低风险，20-30%中等风险，>30%高风险',
};

// 绩效指标说明
export const PERFORMANCE_METRIC_DESCRIPTIONS = {
  annualized_return:
    '年化收益率\n计算方法：(1 + 总收益率)^(365/持有天数) - 1\n重要性：反映策略的整体盈利能力\n参考标准：>15%优秀，8-15%良好，<8%待改进',
  sharpe_ratio:
    '夏普比率\n计算方法：(年化收益率 - 无风险利率) / 年化波动率\n重要性：衡量风险调整后的收益水平\n参考标准：>2优秀，1-2良好，<1待改进',
  win_rate:
    '胜率\n计算方法：盈利交易次数 / 总交易次数\n重要性：反映策略的准确性\n参考标准：>60%优秀，50-60%良好，<50%待改进',
  profit_loss_ratio:
    '盈亏比\n计算方法：总盈利 / 总亏损\n重要性：反映策略的整体效率\n参考标准：>2优秀，1.5-2良好，<1.5待改进',
  max_gain_info:
    '最大收益\n计算方法：单次交易中的最大盈利百分比\n重要性：反映把握机会能力\n包含具体交易信息：股票代码、盈利比例、交易时间',
  max_loss_info:
    '最大回撤\n计算方法：单次交易中的最大亏损百分比\n重要性：反映风险控制能力\n包含具体交易信息：股票代码、亏损比例、交易时间',
  avg_holding_days:
    '平均持仓天数\n计算方法：所有交易的平均持仓天数\n重要性：反映策略的交易周期\n建议：需要结合策略类型具体分析',
};

// 市场状态说明
export const MARKET_REGIME_DESCRIPTIONS = {
  BULLISH:
    '牛市状态\n特征：市场呈现明显上涨趋势，成交量温和放大\n策略：采用满仓策略，偏向进取',
  BEARISH:
    '熊市状态\n特征：市场呈现明显下跌趋势，恐慌情绪加重\n策略：采用减仓策略（70%），注重防守',
  VOLATILE:
    '震荡市状态\n特征：市场波动剧烈，方向不明确\n策略：采用保守策略（半仓），控制风险',
  NEUTRAL:
    '盘整市状态\n特征：市场无明显趋势，波动相对平稳\n策略：采用适中策略（90%仓位），均衡配置',
};

// 仓位管理说明
export const POSITION_SIZING_DESCRIPTIONS = {
  base_size:
    '基础仓位\n计算方法：根据市场状态和资金量确定的标准仓位\n重要性：是仓位管理的基础参考',
  vol_scalar:
    '波动率调整因子\n计算方法：exp(-波动率系数×当前波动率)\n重要性：在高波动时自动降低仓位',
  momentum_scalar:
    '动量调整因子\n计算方法：基于价格趋势强度计算\n重要性：跟随趋势调整仓位大小',
  max_position:
    '最大持仓比例\n计算方法：单个持仓的最大允许比例\n重要性：控制单一持仓风险',
  max_portfolio_vol:
    '最大组合波动率\n计算方法：年化的组合收益率标准差上限\n重要性：控制整体组合风险水平',
};
