import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { useMediaQuery } from '@mui/material';
import NavBar from '../../../components/layout/NavBar';
import { renderWithProviders } from '../../utils/testUtils';

// Mock useMediaQuery for responsive testing
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material');
  return {
    ...actual,
    useMediaQuery: vi.fn(),
  };
});

describe('NavBar Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Default to desktop view
    useMediaQuery.mockReturnValue(false);
  });

  describe('Desktop Navigation', () => {
    it('renders the logo', () => {
      renderWithProviders(<NavBar />);
      expect(screen.getAllByText('股票管理系统')[0]).toBeInTheDocument();
    });

    it('renders all navigation menu items', () => {
      renderWithProviders(<NavBar />);

      expect(screen.getByRole('link', { name: /主页/ })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /账户/ })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /策略/ })).toBeInTheDocument();
      expect(screen.getByRole('link', { name: /信息/ })).toBeInTheDocument();
    });

    it('highlights the active route', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/accounts'] });

      const accountsButton = screen.getByRole('link', { name: /账户/ });
      expect(accountsButton).toHaveAttribute('href', '/accounts');
    });

    it('navigates to correct routes when clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      const homeLink = screen.getByRole('link', { name: /主页/ });
      const accountsLink = screen.getByRole('link', { name: /账户/ });
      const strategiesLink = screen.getByRole('link', { name: /策略/ });
      const referenceLink = screen.getByRole('link', { name: /信息/ });

      expect(homeLink).toHaveAttribute('href', '/');
      expect(accountsLink).toHaveAttribute('href', '/accounts');
      expect(strategiesLink).toHaveAttribute('href', '/strategies');
      expect(referenceLink).toHaveAttribute('href', '/reference');
    });

    it('displays navigation icons', () => {
      renderWithProviders(<NavBar />);

      // Check for MUI icons (they should be rendered as SVG elements)
      const icons = screen.getAllByTestId(/.*Icon$/);
      expect(icons.length).toBeGreaterThan(0);
    });

    it('does not show mobile menu button in desktop view', () => {
      useMediaQuery.mockReturnValue(false);
      renderWithProviders(<NavBar />);

      expect(screen.queryByTestId('MenuIcon')).not.toBeInTheDocument();
    });
  });

  describe('Mobile Navigation', () => {
    beforeEach(() => {
      // Mock mobile view
      useMediaQuery.mockReturnValue(true);
    });

    it('shows mobile menu button in mobile view', () => {
      renderWithProviders(<NavBar />);

      const menuButton = screen.getByRole('button');
      expect(menuButton).toBeInTheDocument();
    });

    it('hides desktop navigation items in mobile view', () => {
      renderWithProviders(<NavBar />);

      // Desktop navigation buttons should not be visible (only mobile menu button should be visible)
      expect(
        screen.queryByRole('link', { name: /主页/ })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: /账户/ })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: /策略/ })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByRole('link', { name: /信息/ })
      ).not.toBeInTheDocument();
    });

    it('opens mobile drawer when menu button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      // Drawer should be open and show navigation items
      await waitFor(() => {
        expect(screen.getByRole('presentation')).toBeInTheDocument();
      });
    });

    it('closes mobile drawer when close button is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      // Open drawer
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      await waitFor(() => {
        expect(screen.getByRole('presentation')).toBeInTheDocument();
      });

      // Close drawer
      const closeButton = screen.getByTestId('CloseIcon').closest('button');
      await user.click(closeButton);

      await waitFor(() => {
        expect(screen.queryByRole('presentation')).not.toBeInTheDocument();
      });
    });

    it('closes drawer when navigation item is clicked', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      // Open drawer
      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      await waitFor(() => {
        expect(screen.getByRole('presentation')).toBeInTheDocument();
      });

      // Click on a navigation item
      const homeLink = screen.getByText('主页');
      await user.click(homeLink);

      // Drawer should close
      await waitFor(() => {
        expect(screen.queryByRole('presentation')).not.toBeInTheDocument();
      });
    });

    it('shows all navigation items in mobile drawer', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      await waitFor(() => {
        expect(screen.getAllByText('主页')).toHaveLength(1);
        expect(screen.getAllByText('账户')).toHaveLength(1);
        expect(screen.getAllByText('策略')).toHaveLength(1);
        expect(screen.getAllByText('信息')).toHaveLength(1);
      });
    });
  });

  describe('Active State Handling', () => {
    it('shows correct active state for home page', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/'] });

      const homeLink = screen.getByRole('link', { name: /主页/ });
      expect(homeLink).toHaveAttribute('href', '/');
    });

    it('shows correct active state for accounts page', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/accounts'] });

      const accountsLink = screen.getByRole('link', { name: /账户/ });
      expect(accountsLink).toHaveAttribute('href', '/accounts');
    });

    it('shows correct active state for strategies page', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/strategies'] });

      const strategiesLink = screen.getByRole('link', { name: /策略/ });
      expect(strategiesLink).toHaveAttribute('href', '/strategies');
    });

    it('shows correct active state for reference page', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/reference'] });

      const referenceLink = screen.getByRole('link', { name: /信息/ });
      expect(referenceLink).toHaveAttribute('href', '/reference');
    });

    it('handles unknown routes without active state', () => {
      renderWithProviders(<NavBar />, { initialEntries: ['/unknown'] });

      // No navigation item should be active for unknown routes
      const homeLink = screen.getByRole('link', { name: /主页/ });
      expect(homeLink).toHaveAttribute('href', '/');
    });
  });

  describe('Styling and Theme', () => {
    it('applies correct styling classes', () => {
      renderWithProviders(<NavBar />);

      const appBar = screen.getByRole('banner');
      expect(appBar).toBeInTheDocument();
    });

    it('has proper gradient background', () => {
      renderWithProviders(<NavBar />);

      const appBar = screen.getByRole('banner');
      expect(appBar).toBeInTheDocument();
    });

    it('displays logo with correct styling', () => {
      renderWithProviders(<NavBar />);

      const logo = screen.getByRole('heading', { name: '股票管理系统' });
      expect(logo).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels', () => {
      renderWithProviders(<NavBar />);

      const navigation = screen.getByRole('banner');
      expect(navigation).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup();
      renderWithProviders(<NavBar />);

      // Tab through navigation items
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });

    it('has proper focus management in mobile drawer', async () => {
      const user = userEvent.setup();
      useMediaQuery.mockReturnValue(true);
      renderWithProviders(<NavBar />);

      const menuButton = screen.getByRole('button');
      await user.click(menuButton);

      await waitFor(() => {
        expect(screen.getByRole('presentation')).toBeInTheDocument();
      });

      // Focus should be manageable within the drawer
      await user.tab();
      expect(document.activeElement).toBeInTheDocument();
    });
  });

  describe('Responsive Behavior', () => {
    it('switches between desktop and mobile layouts', () => {
      const { rerender } = renderWithProviders(<NavBar />);

      // Desktop layout
      useMediaQuery.mockReturnValue(false);
      rerender(<NavBar />);
      expect(screen.getByRole('link', { name: /主页/ })).toBeInTheDocument();

      // Mobile layout
      useMediaQuery.mockReturnValue(true);
      rerender(<NavBar />);
      expect(screen.getByRole('button')).toBeInTheDocument();
    });

    it('handles breakpoint changes gracefully', () => {
      useMediaQuery.mockReturnValue(false);
      const { rerender } = renderWithProviders(<NavBar />);

      // Change to mobile
      useMediaQuery.mockReturnValue(true);
      rerender(<NavBar />);

      // Should not crash and should show mobile layout
      expect(screen.getByRole('button')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('renders efficiently', () => {
      const startTime = performance.now();
      renderWithProviders(<NavBar />);
      const endTime = performance.now();

      expect(endTime - startTime).toBeLessThan(50);
    });

    it('does not cause unnecessary re-renders', () => {
      const { rerender } = renderWithProviders(<NavBar />);

      // Multiple re-renders should not cause issues
      rerender(<NavBar />);
      rerender(<NavBar />);

      expect(
        screen.getByRole('heading', { name: '股票管理系统' })
      ).toBeInTheDocument();
    });
  });
});
