import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { createLogger } from '../../utils/logger';

describe('Logger Utility', () => {
  let logger;
  let consoleSpy;

  beforeEach(() => {
    logger = createLogger('TestComponent');
    // Mock console methods
    consoleSpy = {
      groupCollapsed: vi
        .spyOn(console, 'groupCollapsed')
        .mockImplementation(() => {}),
      log: vi.spyOn(console, 'log').mockImplementation(() => {}),
      groupEnd: vi.spyOn(console, 'groupEnd').mockImplementation(() => {}),
    };
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Logger Creation', () => {
    it('creates logger with component name', () => {
      const testLogger = createLogger('MyComponent');
      expect(testLogger).toBeDefined();
      expect(testLogger.component).toBe('MyComponent');
    });

    it('has all required logging methods', () => {
      expect(typeof logger.debug).toBe('function');
      expect(typeof logger.info).toBe('function');
      expect(typeof logger.warn).toBe('function');
      expect(typeof logger.error).toBe('function');
    });
  });

  describe('Debug Logging', () => {
    it('logs debug messages correctly', () => {
      const message = 'Debug message';
      const data = { key: 'value' };

      logger.debug(message, data);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[DEBUG] [TestComponent]'),
        'color: #6c757d'
      );
      expect(consoleSpy.log).toHaveBeenCalledWith('Message:', message);
      expect(consoleSpy.log).toHaveBeenCalledWith('Data:', data);
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });

    it('logs debug messages without data', () => {
      const message = 'Debug message without data';

      logger.debug(message);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalled();
      expect(consoleSpy.log).toHaveBeenCalledWith('Message:', message);
      expect(consoleSpy.log).not.toHaveBeenCalledWith(
        'Data:',
        expect.anything()
      );
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });
  });

  describe('Info Logging', () => {
    it('logs info messages correctly', () => {
      const message = 'Info message';
      const data = { status: 'success' };

      logger.info(message, data);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[INFO] [TestComponent]'),
        'color: #0d6efd'
      );
      expect(consoleSpy.log).toHaveBeenCalledWith('Message:', message);
      expect(consoleSpy.log).toHaveBeenCalledWith('Data:', data);
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });
  });

  describe('Warning Logging', () => {
    it('logs warning messages correctly', () => {
      const message = 'Warning message';
      const data = { warning: 'deprecated' };

      logger.warn(message, data);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[WARN] [TestComponent]'),
        'color: #ffc107'
      );
      expect(consoleSpy.log).toHaveBeenCalledWith('Message:', message);
      expect(consoleSpy.log).toHaveBeenCalledWith('Data:', data);
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });
  });

  describe('Error Logging', () => {
    it('logs error messages correctly', () => {
      const message = 'Error message';
      const data = { error: 'critical' };

      logger.error(message, data);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[ERROR] [TestComponent]'),
        'color: #dc3545; font-weight: bold'
      );
      expect(consoleSpy.log).toHaveBeenCalledWith('Message:', message);
      expect(consoleSpy.log).toHaveBeenCalledWith('Data:', data);
      expect(consoleSpy.groupEnd).toHaveBeenCalled();
    });
  });

  describe('Timestamp Formatting', () => {
    it('includes timestamp in log output', () => {
      const message = 'Test message';

      logger.info(message);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringMatching(
          /\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/
        ),
        expect.any(String)
      );
    });
  });

  describe('Component Name in Logs', () => {
    it('includes component name in all log levels', () => {
      const message = 'Test message';

      logger.debug(message);
      logger.info(message);
      logger.warn(message);
      logger.error(message);

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledTimes(4);
      consoleSpy.groupCollapsed.mock.calls.forEach(call => {
        expect(call[0]).toContain('[TestComponent]');
      });
    });
  });

  describe('Multiple Logger Instances', () => {
    it('maintains separate component names for different loggers', () => {
      const logger1 = createLogger('Component1');
      const logger2 = createLogger('Component2');

      logger1.info('Message from component 1');
      logger2.info('Message from component 2');

      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[Component1]'),
        expect.any(String)
      );
      expect(consoleSpy.groupCollapsed).toHaveBeenCalledWith(
        expect.stringContaining('[Component2]'),
        expect.any(String)
      );
    });
  });

  describe('Data Handling', () => {
    it('handles null data gracefully', () => {
      logger.info('Message with null data', null);

      expect(consoleSpy.log).toHaveBeenCalledWith(
        'Message:',
        'Message with null data'
      );
      expect(consoleSpy.log).not.toHaveBeenCalledWith('Data:', null);
    });

    it('handles undefined data gracefully', () => {
      logger.info('Message with undefined data', undefined);

      expect(consoleSpy.log).toHaveBeenCalledWith(
        'Message:',
        'Message with undefined data'
      );
      expect(consoleSpy.log).not.toHaveBeenCalledWith('Data:', undefined);
    });

    it('handles complex data objects', () => {
      const complexData = {
        nested: { object: true },
        array: [1, 2, 3],
        function: () => {},
        date: new Date(),
      };

      logger.info('Complex data message', complexData);

      expect(consoleSpy.log).toHaveBeenCalledWith('Data:', complexData);
    });
  });
});
