import { describe, it, expect } from 'vitest';
import {
  PERFORMANCE_METRIC_NAMES,
  RISK_METRIC_NAMES,
  RISK_METRIC_DESCRIPTIONS,
  PERFORMANCE_METRIC_DESCRIPTIONS,
  MARKET_REGIME_DESCRIPTIONS,
  POSITION_SIZING_DESCRIPTIONS,
} from '../../constants/metricDescriptions';

describe('Metric Descriptions Constants', () => {
  describe('PERFORMANCE_METRIC_NAMES', () => {
    it('should be defined and be an object', () => {
      expect(PERFORMANCE_METRIC_NAMES).toBeDefined();
      expect(typeof PERFORMANCE_METRIC_NAMES).toBe('object');
    });

    it('should have all expected performance metric names', () => {
      const expectedKeys = [
        'annualized_return',
        'sharpe_ratio',
        'win_rate',
        'profit_loss_ratio',
        'max_gain_info',
        'max_loss_info',
        'avg_holding_days',
      ];

      expectedKeys.forEach(key => {
        expect(PERFORMANCE_METRIC_NAMES).toHaveProperty(key);
        expect(typeof PERFORMANCE_METRIC_NAMES[key]).toBe('string');
        expect(PERFORMANCE_METRIC_NAMES[key].length).toBeGreaterThan(0);
      });
    });

    it('should have Chinese translations', () => {
      expect(PERFORMANCE_METRIC_NAMES.annualized_return).toBe('年化收益率');
      expect(PERFORMANCE_METRIC_NAMES.sharpe_ratio).toBe('夏普比率');
      expect(PERFORMANCE_METRIC_NAMES.win_rate).toBe('胜率');
    });
  });

  describe('RISK_METRIC_NAMES', () => {
    it('should be defined and be an object', () => {
      expect(RISK_METRIC_NAMES).toBeDefined();
      expect(typeof RISK_METRIC_NAMES).toBe('object');
    });

    it('should have all expected risk metric names', () => {
      const expectedKeys = [
        'portfolio_var',
        'portfolio_vol',
        'max_drawdown',
        'sharpe_ratio',
        'risk_level',
        'concentration_risk',
      ];

      expectedKeys.forEach(key => {
        expect(RISK_METRIC_NAMES).toHaveProperty(key);
        expect(typeof RISK_METRIC_NAMES[key]).toBe('string');
        expect(RISK_METRIC_NAMES[key].length).toBeGreaterThan(0);
      });
    });

    it('should have Chinese translations', () => {
      expect(RISK_METRIC_NAMES.portfolio_var).toBe('组合方差');
      expect(RISK_METRIC_NAMES.portfolio_vol).toBe('组合波动率');
      expect(RISK_METRIC_NAMES.max_drawdown).toBe('最大回撤');
    });
  });

  describe('RISK_METRIC_DESCRIPTIONS', () => {
    it('should be defined and be an object', () => {
      expect(RISK_METRIC_DESCRIPTIONS).toBeDefined();
      expect(typeof RISK_METRIC_DESCRIPTIONS).toBe('object');
    });

    it('should have detailed descriptions for all risk metrics', () => {
      const expectedKeys = [
        'portfolio_var',
        'portfolio_vol',
        'max_drawdown',
        'sharpe_ratio',
        'risk_level',
        'concentration_risk',
      ];

      expectedKeys.forEach(key => {
        expect(RISK_METRIC_DESCRIPTIONS).toHaveProperty(key);
        expect(typeof RISK_METRIC_DESCRIPTIONS[key]).toBe('string');
        expect(RISK_METRIC_DESCRIPTIONS[key].length).toBeGreaterThan(20);
      });
    });

    it('should contain calculation methods in descriptions', () => {
      Object.values(RISK_METRIC_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/计算方法/);
      });
    });

    it('should contain importance explanations in descriptions', () => {
      Object.values(RISK_METRIC_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/重要性|计算方法|参考标准/);
      });
    });
  });

  describe('PERFORMANCE_METRIC_DESCRIPTIONS', () => {
    it('should be defined and be an object', () => {
      expect(PERFORMANCE_METRIC_DESCRIPTIONS).toBeDefined();
      expect(typeof PERFORMANCE_METRIC_DESCRIPTIONS).toBe('object');
    });

    it('should have detailed descriptions for all performance metrics', () => {
      const expectedKeys = [
        'annualized_return',
        'sharpe_ratio',
        'win_rate',
        'profit_loss_ratio',
        'max_gain_info',
        'max_loss_info',
        'avg_holding_days',
      ];

      expectedKeys.forEach(key => {
        expect(PERFORMANCE_METRIC_DESCRIPTIONS).toHaveProperty(key);
        expect(typeof PERFORMANCE_METRIC_DESCRIPTIONS[key]).toBe('string');
        expect(PERFORMANCE_METRIC_DESCRIPTIONS[key].length).toBeGreaterThan(20);
      });
    });

    it('should contain calculation methods in descriptions', () => {
      Object.values(PERFORMANCE_METRIC_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/计算方法/);
      });
    });

    it('should contain reference standards in descriptions', () => {
      Object.values(PERFORMANCE_METRIC_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/参考标准|重要性|建议/);
      });
    });
  });

  describe('MARKET_REGIME_DESCRIPTIONS', () => {
    it('should be defined and be an object', () => {
      expect(MARKET_REGIME_DESCRIPTIONS).toBeDefined();
      expect(typeof MARKET_REGIME_DESCRIPTIONS).toBe('object');
    });

    it('should have all market regime types', () => {
      const expectedKeys = ['BULLISH', 'BEARISH', 'VOLATILE', 'NEUTRAL'];

      expectedKeys.forEach(key => {
        expect(MARKET_REGIME_DESCRIPTIONS).toHaveProperty(key);
        expect(typeof MARKET_REGIME_DESCRIPTIONS[key]).toBe('string');
        expect(MARKET_REGIME_DESCRIPTIONS[key].length).toBeGreaterThan(30);
      });
    });

    it('should contain market characteristics in descriptions', () => {
      Object.values(MARKET_REGIME_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/特征/);
      });
    });

    it('should contain strategy recommendations in descriptions', () => {
      Object.values(MARKET_REGIME_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/策略/);
      });
    });

    it('should have correct market regime translations', () => {
      expect(MARKET_REGIME_DESCRIPTIONS.BULLISH).toContain('牛市状态');
      expect(MARKET_REGIME_DESCRIPTIONS.BEARISH).toContain('熊市状态');
      expect(MARKET_REGIME_DESCRIPTIONS.VOLATILE).toContain('震荡市状态');
      expect(MARKET_REGIME_DESCRIPTIONS.NEUTRAL).toContain('盘整市状态');
    });
  });

  describe('POSITION_SIZING_DESCRIPTIONS', () => {
    it('should be defined and be an object', () => {
      expect(POSITION_SIZING_DESCRIPTIONS).toBeDefined();
      expect(typeof POSITION_SIZING_DESCRIPTIONS).toBe('object');
    });

    it('should have all position sizing parameters', () => {
      const expectedKeys = [
        'base_size',
        'vol_scalar',
        'momentum_scalar',
        'max_position',
        'max_portfolio_vol',
      ];

      expectedKeys.forEach(key => {
        expect(POSITION_SIZING_DESCRIPTIONS).toHaveProperty(key);
        expect(typeof POSITION_SIZING_DESCRIPTIONS[key]).toBe('string');
        expect(POSITION_SIZING_DESCRIPTIONS[key].length).toBeGreaterThan(30);
      });
    });

    it('should contain calculation methods in descriptions', () => {
      Object.values(POSITION_SIZING_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/计算方法/);
      });
    });

    it('should contain importance explanations in descriptions', () => {
      Object.values(POSITION_SIZING_DESCRIPTIONS).forEach(description => {
        expect(description).toMatch(/重要性/);
      });
    });
  });

  describe('Cross-Reference Consistency', () => {
    it('should have consistent sharpe_ratio across performance and risk metrics', () => {
      expect(PERFORMANCE_METRIC_NAMES.sharpe_ratio).toBe(
        RISK_METRIC_NAMES.sharpe_ratio
      );
    });

    it('should have descriptions for all named metrics', () => {
      // Check performance metrics
      Object.keys(PERFORMANCE_METRIC_NAMES).forEach(key => {
        expect(PERFORMANCE_METRIC_DESCRIPTIONS).toHaveProperty(key);
      });

      // Check risk metrics
      Object.keys(RISK_METRIC_NAMES).forEach(key => {
        expect(RISK_METRIC_DESCRIPTIONS).toHaveProperty(key);
      });
    });
  });

  describe('Data Integrity', () => {
    it('should not have empty strings in any constant', () => {
      const allConstants = [
        PERFORMANCE_METRIC_NAMES,
        RISK_METRIC_NAMES,
        RISK_METRIC_DESCRIPTIONS,
        PERFORMANCE_METRIC_DESCRIPTIONS,
        MARKET_REGIME_DESCRIPTIONS,
        POSITION_SIZING_DESCRIPTIONS,
      ];

      allConstants.forEach(constant => {
        Object.values(constant).forEach(value => {
          expect(value.trim().length).toBeGreaterThan(0);
        });
      });
    });

    it('should have consistent formatting in descriptions', () => {
      const allDescriptions = {
        ...RISK_METRIC_DESCRIPTIONS,
        ...PERFORMANCE_METRIC_DESCRIPTIONS,
        ...MARKET_REGIME_DESCRIPTIONS,
        ...POSITION_SIZING_DESCRIPTIONS,
      };

      Object.values(allDescriptions).forEach(description => {
        // Should not start or end with whitespace
        expect(description).toBe(description.trim());
        // Should contain Chinese characters
        expect(description).toMatch(/[\u4e00-\u9fff]/);
      });
    });
  });
});
