import { describe, it, expect } from 'vitest';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';

describe('Config Constants', () => {
  describe('API_BASE_URL', () => {
    it('should be defined', () => {
      expect(API_BASE_URL).toBeDefined();
    });

    it('should be a string', () => {
      expect(typeof API_BASE_URL).toBe('string');
    });
  });

  describe('API_ENDPOINTS', () => {
    it('should be defined', () => {
      expect(API_ENDPOINTS).toBeDefined();
    });

    it('should be an object', () => {
      expect(typeof API_ENDPOINTS).toBe('object');
      expect(API_ENDPOINTS).not.toBeNull();
    });

    describe('Core Endpoints', () => {
      it('should have OVERVIEW endpoint', () => {
        expect(API_ENDPOINTS.OVERVIEW).toBe('/api/analytics/overview');
      });

      it('should have ACCOUNTS endpoint', () => {
        expect(API_ENDPOINTS.ACCOUNTS).toBe('/api/accounts');
      });

      it('should have TRANSACTIONS endpoint', () => {
        expect(API_ENDPOINTS.TRANSACTIONS).toBe('/api/transactions');
      });

      it('should have MARKET_HOTSPOTS endpoint', () => {
        expect(API_ENDPOINTS.MARKET_HOTSPOTS).toBe('/api/market/hotspots');
      });

      it('should have STRATEGY_ANALYSIS endpoint', () => {
        expect(API_ENDPOINTS.STRATEGY_ANALYSIS).toBe(
          '/api/strategies/analysis'
        );
      });

      it('should have REALIZED_GAINS endpoint', () => {
        expect(API_ENDPOINTS.REALIZED_GAINS).toBe(
          '/api/analytics/realized_gains'
        );
      });
    });

    describe('Core Endpoints Validation', () => {
      it('should have all core endpoints defined', () => {
        const coreEndpoints = [
          'OVERVIEW',
          'ACCOUNTS',
          'TRANSACTIONS',
          'MARKET_HOTSPOTS',
          'STRATEGY_ANALYSIS',
          'REALIZED_GAINS',
        ];

        coreEndpoints.forEach(endpoint => {
          expect(API_ENDPOINTS).toHaveProperty(endpoint);
          expect(typeof API_ENDPOINTS[endpoint]).toBe('string');
        });
      });
    });

    describe('Endpoint Format Validation', () => {
      it('should have all endpoints starting with /api', () => {
        Object.values(API_ENDPOINTS).forEach(endpoint => {
          expect(endpoint).toMatch(/^\/api/);
        });
      });

      it('should have all endpoints as strings', () => {
        Object.values(API_ENDPOINTS).forEach(endpoint => {
          expect(typeof endpoint).toBe('string');
        });
      });

      it('should not have empty endpoints', () => {
        Object.values(API_ENDPOINTS).forEach(endpoint => {
          expect(endpoint.length).toBeGreaterThan(0);
        });
      });

      it('should not have endpoints with trailing slashes', () => {
        Object.values(API_ENDPOINTS).forEach(endpoint => {
          expect(endpoint).not.toMatch(/\/$/);
        });
      });
    });

    describe('Endpoint Keys', () => {
      const expectedKeys = [
        'OVERVIEW',
        'ACCOUNTS',
        'TRANSACTIONS',
        'MARKET_HOTSPOTS',
        'STRATEGY_ANALYSIS',
        'REALIZED_GAINS',
      ];

      it('should have all expected endpoint keys', () => {
        expectedKeys.forEach(key => {
          expect(API_ENDPOINTS).toHaveProperty(key);
        });
      });

      it('should have at least the core endpoint keys', () => {
        const actualKeys = Object.keys(API_ENDPOINTS);
        expectedKeys.forEach(key => {
          expect(actualKeys).toContain(key);
        });
      });

      it('should have keys in uppercase', () => {
        Object.keys(API_ENDPOINTS).forEach(key => {
          expect(key).toBe(key.toUpperCase());
        });
      });
    });

    describe('Endpoint Grouping', () => {
      it('should have analytics endpoints', () => {
        const analyticsEndpoints = Object.values(API_ENDPOINTS).filter(
          endpoint => endpoint.includes('/analytics/')
        );
        expect(analyticsEndpoints.length).toBeGreaterThan(0);
      });

      it('should have strategies endpoints', () => {
        const strategiesEndpoints = Object.values(API_ENDPOINTS).filter(
          endpoint => endpoint.includes('/strategies/')
        );
        expect(strategiesEndpoints.length).toBeGreaterThan(0);
      });

      it('should have strategy-related endpoints', () => {
        const strategyEndpoints = Object.values(API_ENDPOINTS).filter(
          endpoint => endpoint.includes('/strategies/')
        );
        expect(strategyEndpoints.length).toBeGreaterThanOrEqual(1);
      });
    });

    describe('Endpoint Uniqueness', () => {
      it('should have unique endpoint values', () => {
        const endpoints = Object.values(API_ENDPOINTS);
        const uniqueEndpoints = [...new Set(endpoints)];
        expect(uniqueEndpoints.length).toBe(endpoints.length);
      });
    });
  });
});
