import { vi } from 'vitest';

// Mock API responses
export const mockApiResponses = {
  overview: {
    account_breakdown: [
      {
        account_id: 1,
        name: 'Test Account 1',
        current_value: 10000,
        total_cost: 8000,
        total_gain: 2000,
        gain_rate: 25.0,
        unrealized_gain: 1500,
        realized_gain: 500,
      },
      {
        account_id: 2,
        name: 'Test Account 2',
        current_value: 5000,
        total_cost: 5500,
        total_gain: -500,
        gain_rate: -9.09,
        unrealized_gain: -300,
        realized_gain: -200,
      },
    ],
  },

  accounts: [
    {
      id: 1,
      name: 'Test Account 1',
      current_value: 10000,
      total_cost: 8000,
      created_at: '2024-01-01T00:00:00Z',
    },
    {
      id: 2,
      name: 'Test Account 2',
      current_value: 5000,
      total_cost: 5500,
      created_at: '2024-01-02T00:00:00Z',
    },
  ],

  holdings: {
    holdings: [
      {
        symbol: 'AAPL',
        account_name: 'Test Account 1',
        quantity: 10,
        avg_price: 150.0,
        current_price: 175.0,
        total_cost: 1500.0,
        current_value: 1750.0,
        gain: 250.0,
        return_rate: 16.67,
      },
      {
        symbol: 'GOOGL',
        account_name: 'Test Account 1',
        quantity: 5,
        avg_price: 2500.0,
        current_price: 2400.0,
        total_cost: 12500.0,
        current_value: 12000.0,
        gain: -500.0,
        return_rate: -4.0,
      },
      {
        symbol: 'MSFT',
        account_name: 'Test Account 2',
        quantity: 8,
        avg_price: 300.0,
        current_price: 350.0,
        total_cost: 2400.0,
        current_value: 2800.0,
        gain: 400.0,
        return_rate: 16.67,
      },
    ],
  },

  transactions: [
    {
      id: 1,
      account_id: 1,
      symbol: 'AAPL',
      transaction_type: 'BUY',
      quantity: 10,
      price: 150.0,
      date: '2024-01-01',
      total_amount: 1500.0,
    },
    {
      id: 2,
      account_id: 1,
      symbol: 'GOOGL',
      transaction_type: 'BUY',
      quantity: 5,
      price: 2500.0,
      date: '2024-01-02',
      total_amount: 12500.0,
    },
  ],

  strategyAnalysis: {
    positions: [
      {
        symbol: 'AAPL',
        current_price: 175.0,
        avg_price: 150.0,
        quantity: 10,
        rsi: 65.5,
        recommendation: 'HOLD',
        volatility: 0.25,
        trend_strength: 0.7,
      },
      {
        symbol: 'GOOGL',
        current_price: 2400.0,
        avg_price: 2500.0,
        quantity: 5,
        rsi: 45.2,
        recommendation: 'BUY',
        volatility: 0.3,
        trend_strength: 0.6,
      },
    ],
    summary: {
      total_positions: 2,
      buy_recommendations: 1,
      sell_recommendations: 0,
      hold_recommendations: 1,
    },
  },

  detailedRecommendation: {
    summary: 'Based on technical analysis, AAPL shows strong momentum.',
    reasons: [
      'RSI indicates oversold conditions',
      'Price above 50-day moving average',
      'Strong earnings growth expected',
    ],
    action_plan: [
      'Consider adding to position on dips',
      'Set stop-loss at $160',
      'Target price: $200',
    ],
  },

  marketHotspots: [
    {
      symbol: 'AAPL',
      price_change: 5.25,
      percent_change: 3.1,
      volume: 50000000,
      market_cap: 2800000000000,
    },
    {
      symbol: 'GOOGL',
      price_change: -12.5,
      percent_change: -0.5,
      volume: 25000000,
      market_cap: 1600000000000,
    },
  ],

  fearGreedIndex: {
    value: 65,
    classification: 'Greed',
    last_update: '2024-01-01T12:00:00Z',
    previous_close: 62,
  },
};

// Mock fetch function
export const createMockFetch = (responses = {}) => {
  return vi.fn().mockImplementation(url => {
    // Extract endpoint from URL
    const endpoint = url.replace(/^.*\/api/, '/api');

    // Default successful response
    let responseData = { success: true };

    // Map endpoints to mock data
    if (endpoint.includes('/analytics/overview')) {
      responseData = responses.overview || mockApiResponses.overview;
    } else if (endpoint.includes('/accounts')) {
      responseData = responses.accounts || mockApiResponses.accounts;
    } else if (endpoint.includes('/analytics/holdings')) {
      responseData = responses.holdings || mockApiResponses.holdings;
    } else if (endpoint.includes('/transactions')) {
      responseData = responses.transactions || mockApiResponses.transactions;
    } else if (endpoint.includes('/strategies/analysis')) {
      responseData =
        responses.strategyAnalysis || mockApiResponses.strategyAnalysis;
    } else if (endpoint.includes('/strategies/detailed-recommendation')) {
      responseData =
        responses.detailedRecommendation ||
        mockApiResponses.detailedRecommendation;
    } else if (endpoint.includes('/market/hotspots')) {
      responseData =
        responses.marketHotspots || mockApiResponses.marketHotspots;
    } else if (endpoint.includes('/market/fear-greed-index')) {
      responseData =
        responses.fearGreedIndex || mockApiResponses.fearGreedIndex;
    }

    return Promise.resolve({
      ok: true,
      status: 200,
      statusText: 'OK',
      headers: new Map([['content-type', 'application/json']]),
      json: () => Promise.resolve(responseData),
    });
  });
};

// Mock fetch with error responses
export const createMockFetchWithErrors = (errorEndpoints = {}) => {
  return vi.fn().mockImplementation(url => {
    const endpoint = url.replace(/^.*\/api/, '/api');

    // Check if this endpoint should return an error
    for (const [errorEndpoint, errorConfig] of Object.entries(errorEndpoints)) {
      if (endpoint.includes(errorEndpoint)) {
        return Promise.resolve({
          ok: false,
          status: errorConfig.status || 500,
          statusText: errorConfig.statusText || 'Internal Server Error',
          headers: new Map([['content-type', 'application/json']]),
          json: () =>
            Promise.resolve({
              error: errorConfig.message || 'Server error',
            }),
        });
      }
    }

    // Default to successful response
    return createMockFetch()(url);
  });
};

// Mock axios responses
export const mockAxiosResponses = {
  get: vi.fn(),
  post: vi.fn(),
  put: vi.fn(),
  delete: vi.fn(),
};

// Setup axios mocks
export const setupAxiosMocks = (customResponses = {}) => {
  const responses = { ...mockApiResponses, ...customResponses };

  mockAxiosResponses.get.mockImplementation(url => {
    const endpoint = url.replace(/^.*\/api/, '/api');

    let data = { success: true };
    if (endpoint.includes('/analytics/overview')) {
      data = responses.overview;
    } else if (endpoint.includes('/accounts')) {
      data = responses.accounts;
    } else if (endpoint.includes('/analytics/holdings')) {
      data = responses.holdings;
    } else if (endpoint.includes('/strategies/analysis')) {
      data = responses.strategyAnalysis;
    }

    return Promise.resolve({ data, status: 200, statusText: 'OK' });
  });

  mockAxiosResponses.post.mockImplementation((url, requestData) => {
    const endpoint = url.replace(/^.*\/api/, '/api');

    let data = { success: true };
    if (endpoint.includes('/accounts')) {
      data = { id: 3, ...requestData };
    } else if (endpoint.includes('/strategies/detailed-recommendation')) {
      data = responses.detailedRecommendation;
    }

    return Promise.resolve({ data, status: 201, statusText: 'Created' });
  });

  return mockAxiosResponses;
};

// Reset all mocks
export const resetApiMocks = () => {
  vi.clearAllMocks();
  Object.values(mockAxiosResponses).forEach(mock => mock.mockClear());
};
