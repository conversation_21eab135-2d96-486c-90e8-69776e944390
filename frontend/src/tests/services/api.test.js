import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { apiRequest, get, post, put, del } from '../../services/api';

// Mock the config
vi.mock('../../constants/config', () => ({
  API_BASE_URL: 'http://localhost:5001',
}));

describe('API Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    global.fetch = vi.fn();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('apiRequest', () => {
    it('makes successful GET request', async () => {
      const mockData = { success: true, data: 'test' };
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: () => Promise.resolve(mockData),
      });

      const result = await apiRequest('/test');

      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:5001/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Accept: 'application/json',
          }),
          credentials: 'include',
        })
      );
      expect(result).toEqual(mockData);
    });

    it('includes custom headers', async () => {
      const mockData = { success: true };
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: () => Promise.resolve(mockData),
      });

      await apiRequest('/test', {
        headers: {
          Authorization: 'Bearer token123',
          'Custom-Header': 'custom-value',
        },
      });

      expect(global.fetch).toHaveBeenCalledWith(
        'http://localhost:5001/test',
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: 'Bearer token123',
            'Custom-Header': 'custom-value',
          }),
        })
      );
    });

    it('handles non-JSON response content type', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'text/html']]),
        json: () => Promise.resolve({}),
      });

      await expect(apiRequest('/test')).rejects.toThrow(
        '服务器返回了非JSON响应: text/html'
      );
    });

    it('handles missing content type', async () => {
      global.fetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        headers: new Map(),
        json: () => Promise.resolve({}),
      });

      await expect(apiRequest('/test')).rejects.toThrow(
        '服务器返回了非JSON响应: undefined'
      );
    });

    describe('Error Handling', () => {
      it('handles 400 Bad Request', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 400,
          statusText: 'Bad Request',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Invalid parameters' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Invalid parameters');
      });

      it('handles 401 Unauthorized', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 401,
          statusText: 'Unauthorized',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Not authorized' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Not authorized');
      });

      it('handles 403 Forbidden', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 403,
          statusText: 'Forbidden',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Access denied' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Access denied');
      });

      it('handles 404 Not Found', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 404,
          statusText: 'Not Found',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Resource not found' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Resource not found');
      });

      it('handles 500 Internal Server Error', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 500,
          statusText: 'Internal Server Error',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Server error' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Server error');
      });

      it('handles unknown status codes', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 418,
          statusText: "I'm a teapot",
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Teapot error' }),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Teapot error');
      });

      it('handles errors without custom message', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: false,
          status: 400,
          statusText: 'Bad Request',
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({}),
        });

        await expect(apiRequest('/test')).rejects.toThrow('请求参数错误');
      });

      it('handles network errors', async () => {
        global.fetch.mockRejectedValueOnce(new Error('Network error'));

        await expect(apiRequest('/test')).rejects.toThrow('Network error');
      });

      it('handles JSON parsing errors', async () => {
        global.fetch.mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.reject(new Error('Invalid JSON')),
        });

        await expect(apiRequest('/test')).rejects.toThrow('Invalid JSON');
      });
    });
  });

  describe('HTTP Method Helpers', () => {
    beforeEach(() => {
      global.fetch.mockResolvedValue({
        ok: true,
        status: 200,
        headers: new Map([['content-type', 'application/json']]),
        json: () => Promise.resolve({ success: true }),
      });
    });

    describe('get', () => {
      it('makes GET request', async () => {
        await get('/test-endpoint');

        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5001/test-endpoint',
          expect.objectContaining({
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              Accept: 'application/json',
            }),
            credentials: 'include',
          })
        );
      });
    });

    describe('post', () => {
      it('makes POST request with data', async () => {
        const testData = { name: 'test', value: 123 };
        await post('/test-endpoint', testData);

        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5001/test-endpoint',
          expect.objectContaining({
            method: 'POST',
            body: JSON.stringify(testData),
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              Accept: 'application/json',
            }),
          })
        );
      });

      it('handles empty data', async () => {
        await post('/test-endpoint');

        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5001/test-endpoint',
          expect.objectContaining({
            method: 'POST',
            body: undefined,
          })
        );
      });
    });

    describe('put', () => {
      it('makes PUT request with data', async () => {
        const testData = { id: 1, name: 'updated' };
        await put('/test-endpoint', testData);

        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5001/test-endpoint',
          expect.objectContaining({
            method: 'PUT',
            body: JSON.stringify(testData),
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              Accept: 'application/json',
            }),
          })
        );
      });
    });

    describe('del', () => {
      it('makes DELETE request', async () => {
        await del('/test-endpoint');

        expect(global.fetch).toHaveBeenCalledWith(
          'http://localhost:5001/test-endpoint',
          expect.objectContaining({
            method: 'DELETE',
            headers: expect.objectContaining({
              'Content-Type': 'application/json',
              Accept: 'application/json',
            }),
          })
        );
      });
    });
  });

  describe('Integration Scenarios', () => {
    it('handles successful API workflow', async () => {
      // Mock successful responses
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ accounts: [] }),
        })
        .mockResolvedValueOnce({
          ok: true,
          status: 201,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ id: 1, name: 'New Account' }),
        });

      // Get accounts
      const accounts = await get('/accounts');
      expect(accounts).toEqual({ accounts: [] });

      // Create account
      const newAccount = await post('/accounts', { name: 'New Account' });
      expect(newAccount).toEqual({ id: 1, name: 'New Account' });
    });

    it('handles mixed success and error responses', async () => {
      global.fetch
        .mockResolvedValueOnce({
          ok: true,
          status: 200,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ success: true }),
        })
        .mockResolvedValueOnce({
          ok: false,
          status: 400,
          headers: new Map([['content-type', 'application/json']]),
          json: () => Promise.resolve({ error: 'Validation failed' }),
        });

      // First request succeeds
      const result1 = await get('/test1');
      expect(result1).toEqual({ success: true });

      // Second request fails
      await expect(get('/test2')).rejects.toThrow('Validation failed');
    });
  });
});
