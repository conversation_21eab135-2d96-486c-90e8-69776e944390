import React, { useEffect, useRef, useState } from 'react';
import { styled } from '@mui/material/styles';
import {
  Box,
  Button,
  DialogContent,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  TableSortLabel,
  Paper,
  Typography,
} from '@mui/material';
import {
  ChartContainer,
  GradientTypography,
  PrimaryButton,
  ModernDialog,
  ModernDialogTitle,
  ModernTableContainer,
  ModernTableHeadCell,
  ModernTableRow,
  ValueCell as SharedValueCell,
} from '../../styles/common';
import * as d3 from 'd3';
import { API_BASE_URL } from '../../constants/config';

// ChartContainer now imported from common.js

const ChartWrapper = styled('div')({
  width: '100%',
  height: '100%',
  position: 'relative',
  '& svg': {
    width: '100%',
    height: '100%',
    display: 'block',
  },
});

const MoreDetailsButton = styled(PrimaryButton)(({ theme }) => ({
  position: 'absolute',
  bottom: '16px',
  left: '50%',
  transform: 'translateX(-50%)',
  borderRadius: '24px',
  padding: '10px 24px',
  fontSize: '0.875rem',
  zIndex: 10,
  minWidth: '140px',
  '&:hover': {
    transform: 'translate(-50%, -2px)',
  },
}));

const LegendWrapper = styled('div')({
  position: 'absolute',
  right: '20px',
  top: '20px',
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(8px)',
  padding: '12px',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
  border: '1px solid rgba(255,255,255,0.2)',
});

const LegendItem = styled('div')({
  display: 'flex',
  alignItems: 'center',
  marginBottom: '8px',
  '& .color-box': {
    width: '12px',
    height: '12px',
    marginRight: '8px',
    borderRadius: '3px',
    boxShadow: '0 1px 3px rgba(0,0,0,0.2)',
  },
});

// StyledTableCell and ValueCell now imported from common.js as ModernTableHeadCell and SharedValueCell

// StyledDialog, StyledTableContainer, and StyledTableRow now imported from common.js

function CompositeChart() {
  const svgRef = useRef(null);
  const [data, setData] = useState([]);
  const [hasMore, setHasMore] = useState(false);
  const [openDialog, setOpenDialog] = useState(false);
  const [orderBy, setOrderBy] = useState('value'); // 默认按市值排序
  const [order, setOrder] = useState('desc'); // 默认降序

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/api/analytics/holdings`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        const holdings = result.holdings || [];
        setData(holdings);
        // 当数据超过8个时显示More details按钮
        setHasMore(holdings.length > 8);
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };
    fetchData();
  }, []);

  useEffect(() => {
    if (!data.length || !svgRef.current) return;

    const width = svgRef.current.clientWidth;
    const height = svgRef.current.clientHeight - (hasMore ? 60 : 0); // 为按钮预留空间
    const padding = {
      top: 10,
      right: 10,
      bottom: 10,
      left: 10,
    };

    const svg = d3.select(svgRef.current);
    svg.selectAll('*').remove();

    // 创建treemap布局
    const treemap = d3
      .treemap()
      .size([
        width - padding.left - padding.right,
        height - padding.top - padding.bottom,
      ])
      .paddingOuter(12)
      .paddingInner(12)
      .round(true);

    // 准备数据
    const root = d3
      .hierarchy({ children: data })
      .sum(d => d.value)
      .sort((a, b) => b.value - a.value);

    // 应用treemap布局
    treemap(root);

    // 创建容器组
    const g = svg
      .append('g')
      .attr('transform', `translate(${padding.left},${padding.top})`);

    // 添加方块
    const cell = g
      .selectAll('g')
      .data(root.leaves())
      .enter()
      .append('g')
      .attr('transform', d => `translate(${d.x0},${d.y0})`);

    // 添加方块背景
    cell
      .append('rect')
      .attr('width', d => Math.max(0, d.x1 - d.x0))
      .attr('height', d => Math.max(0, d.y1 - d.y0))
      .attr('fill', d => {
        const returnRate = (d.data.gains / (d.data.value - d.data.gains)) * 100;
        return returnRate >= 0 ? '#f0f9f1' : '#fff1f0';
      })
      .attr('rx', 8)
      .attr('ry', 8);

    // 添加股票代码和收益率
    cell.each(function (d) {
      const width = d.x1 - d.x0;
      const height = d.y1 - d.y0;
      const minDimension = Math.min(width, height);

      // 只在足够大的方块中显示文字（增加阈值）
      if (minDimension > 30) {
        const cellGroup = d3.select(this);
        const returnRate = (d.data.gains / (d.data.value - d.data.gains)) * 100;

        // 添加股票代码
        cellGroup
          .append('text')
          .attr('x', width / 2)
          .attr('y', height / 2 - 8)
          .attr('text-anchor', 'middle')
          .attr('dominant-baseline', 'middle')
          .style('font-size', Math.min(14, minDimension / 6) + 'px')
          .style('font-weight', '500')
          .style('fill', '#333')
          .text(d.data.symbol);

        // 添加收益率
        cellGroup
          .append('text')
          .attr('x', width / 2)
          .attr('y', height / 2 + 8)
          .attr('text-anchor', 'middle')
          .style('font-size', Math.min(12, minDimension / 7) + 'px')
          .style('fill', returnRate >= 0 ? '#4caf50' : '#ef5350')
          .text(`${returnRate >= 0 ? '+' : ''}${returnRate.toFixed(2)}%`);
      }
    });
  }, [data, hasMore]);

  const handleMoreDetails = () => {
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
  };

  const handleRequestSort = property => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const getSortedData = () => {
    const sortedData = [...data];
    sortedData.sort((a, b) => {
      let aValue, bValue;

      switch (orderBy) {
        case 'symbol':
          return order === 'asc'
            ? a.symbol.localeCompare(b.symbol)
            : b.symbol.localeCompare(a.symbol);
        case 'quantity':
          return order === 'asc'
            ? a.quantity - b.quantity
            : b.quantity - a.quantity;
        case 'value':
          return order === 'asc' ? a.value - b.value : b.value - a.value;
        case 'returnRate':
          aValue = (a.gains / (a.value - a.gains)) * 100;
          bValue = (b.gains / (b.value - b.gains)) * 100;
          return order === 'asc' ? aValue - bValue : bValue - aValue;
        case 'gains':
          return order === 'asc' ? a.gains - b.gains : b.gains - a.gains;
        case 'account_name':
          return order === 'asc'
            ? a.account_name.localeCompare(b.account_name)
            : b.account_name.localeCompare(a.account_name);
        default:
          return 0;
      }
    });
    return sortedData;
  };

  return (
    <ChartContainer>
      <GradientTypography variant="h6" sx={{ mb: 2, fontSize: '1.2rem' }}>
        持仓表现
      </GradientTypography>
      <Box
        sx={{
          flex: 1,
          position: 'relative',
          minHeight: 0,
          backgroundColor: 'transparent',
          borderRadius: 2,
          overflow: 'visible',
        }}
      >
        <ChartWrapper>
          <svg ref={svgRef} />
        </ChartWrapper>
        {hasMore && (
          <MoreDetailsButton
            variant="text"
            onClick={handleMoreDetails}
            size="small"
          >
            More details
          </MoreDetailsButton>
        )}
      </Box>

      <ModernDialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <ModernDialogTitle>持仓详情</ModernDialogTitle>
        <DialogContent>
          <ModernTableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <ModernTableHeadCell>
                    <TableSortLabel
                      active={orderBy === 'symbol'}
                      direction={orderBy === 'symbol' ? order : 'asc'}
                      onClick={() => handleRequestSort('symbol')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      股票代码
                    </TableSortLabel>
                  </ModernTableHeadCell>
                  <ModernTableHeadCell>
                    <TableSortLabel
                      active={orderBy === 'account_name'}
                      direction={orderBy === 'account_name' ? order : 'asc'}
                      onClick={() => handleRequestSort('account_name')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      账户
                    </TableSortLabel>
                  </ModernTableHeadCell>
                  <ModernTableHeadCell align="right">
                    <TableSortLabel
                      active={orderBy === 'quantity'}
                      direction={orderBy === 'quantity' ? order : 'asc'}
                      onClick={() => handleRequestSort('quantity')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      持仓数量
                    </TableSortLabel>
                  </ModernTableHeadCell>
                  <ModernTableHeadCell align="right">
                    <TableSortLabel
                      active={orderBy === 'value'}
                      direction={orderBy === 'value' ? order : 'asc'}
                      onClick={() => handleRequestSort('value')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      市值 (USD)
                    </TableSortLabel>
                  </ModernTableHeadCell>
                  <ModernTableHeadCell align="right">
                    <TableSortLabel
                      active={orderBy === 'returnRate'}
                      direction={orderBy === 'returnRate' ? order : 'asc'}
                      onClick={() => handleRequestSort('returnRate')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      收益率
                    </TableSortLabel>
                  </ModernTableHeadCell>
                  <ModernTableHeadCell align="right">
                    <TableSortLabel
                      active={orderBy === 'gains'}
                      direction={orderBy === 'gains' ? order : 'asc'}
                      onClick={() => handleRequestSort('gains')}
                      sx={{
                        color: '#1976D2 !important',
                        fontWeight: '700',
                        '& .MuiTableSortLabel-icon': {
                          color: '#1976D2 !important',
                        },
                      }}
                    >
                      收益额 (USD)
                    </TableSortLabel>
                  </ModernTableHeadCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getSortedData().map((holding, index) => {
                  const returnRate =
                    (holding.gains / (holding.value - holding.gains)) * 100;
                  return (
                    <ModernTableRow
                      key={`${holding.symbol}-${holding.account_name}`}
                      sx={{
                        animation: `slideInTable 0.5s ease-out ${index * 0.05}s both`,
                        '@keyframes slideInTable': {
                          '0%': {
                            opacity: 0,
                            transform: 'translateX(-20px)',
                          },
                          '100%': {
                            opacity: 1,
                            transform: 'translateX(0)',
                          },
                        },
                      }}
                    >
                      <TableCell sx={{ fontWeight: '600', color: '#1976D2' }}>
                        {holding.symbol}
                      </TableCell>
                      <TableCell sx={{ fontWeight: '500' }}>
                        {holding.account_name}
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: '500' }}>
                        {(holding.quantity || 0).toLocaleString()}
                      </TableCell>
                      <TableCell align="right" sx={{ fontWeight: '500' }}>
                        ${(holding.value || 0).toLocaleString()}
                      </TableCell>
                      <SharedValueCell align="right" value={returnRate}>
                        {returnRate >= 0 ? '+' : ''}
                        {(returnRate || 0).toFixed(2)}%
                      </SharedValueCell>
                      <SharedValueCell align="right" value={holding.gains}>
                        ${(holding.gains || 0).toLocaleString()}
                      </SharedValueCell>
                    </ModernTableRow>
                  );
                })}
              </TableBody>
            </Table>
          </ModernTableContainer>
        </DialogContent>
      </ModernDialog>
    </ChartContainer>
  );
}

export default CompositeChart;
