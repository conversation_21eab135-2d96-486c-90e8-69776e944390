import React, { useState } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Alert,
  LinearProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  IconButton,
  Chip,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  CloudUpload as CloudUploadIcon,
} from '@mui/icons-material';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';

function ImportTransactionsDialog({ open, onClose, onSuccess, accountId }) {
  const [files, setFiles] = useState([]);
  const [currentFile, setCurrentFile] = useState(null);
  const [currentFileIndex, setCurrentFileIndex] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [activeStep, setActiveStep] = useState(0);
  const [stageProgress, setStageProgress] = useState({
    parsing: 0,
    validating: 0,
    importing: 0,
  });
  const [failedRecords, setFailedRecords] = useState([]);
  const [importStatus, setImportStatus] = useState(null);
  const [totalImported, setTotalImported] = useState(0);

  const steps = [
    { label: '解析CSV', description: '读取并解析文件内容' },
    { label: '验证数据', description: '检查交易记录的有效性' },
    { label: '保存数据', description: '保存有效的交易记录' },
  ];

  const handleFileChange = event => {
    const selectedFiles = Array.from(event.target.files);
    const validFiles = selectedFiles.filter(file => file.type === 'text/csv');

    if (validFiles.length > 0) {
      setFiles(prev => [...prev, ...validFiles]);
      setError(null);
    } else {
      setError('请选择CSV文件');
    }
  };

  const removeFile = index => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const checkImportStatus = async jobId => {
    try {
      const response = await fetch(
        `${API_BASE_URL}/api/transactions/import-status/${jobId}`,
        {
          headers: {
            Accept: 'application/json',
          },
        }
      );

      // 检查响应的内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        throw new Error(`服务器返回了非JSON响应: ${contentType}`);
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `状态检查失败 (${response.status})`);
      }

      setImportStatus(result);

      // 更新进度
      if (result.stage === 'parsing') {
        setActiveStep(0);
        setStageProgress(prev => ({ ...prev, parsing: result.progress }));
      } else if (result.stage === 'validating') {
        setActiveStep(1);
        setStageProgress(prev => ({ ...prev, validating: result.progress }));
      } else if (result.stage === 'importing') {
        setActiveStep(2);
        setStageProgress(prev => ({ ...prev, importing: result.progress }));
      }

      // 处理失败记录
      if (result.failed_records && result.failed_records.length > 0) {
        setFailedRecords(prev => {
          const newRecords = [...prev];
          result.failed_records.forEach(record => {
            if (
              !newRecords.some(
                r =>
                  r.row === record.row &&
                  r.symbol === record.symbol &&
                  r.error === record.error
              )
            ) {
              newRecords.push(record);
            }
          });
          return newRecords;
        });
      }

      // 检查是否完成
      if (result.status === 'completed') {
        setLoading(false);

        if (onSuccess) {
          await onSuccess(result.success_count || 0);
        }

        if (onSuccess) {
          setFiles([]);
          onClose();
          setFailedRecords([]);
          setTotalImported(0);
        }
      } else if (result.status === 'failed') {
        throw new Error(result.error || '导入失败');
      } else {
        // 继续轮询
        setTimeout(() => checkImportStatus(jobId), 1000);
      }
    } catch (error) {
      console.error('Error checking import status:', error);
      setError(error.message || '检查导入状态时出错');
      setLoading(false);
    }
  };

  const handleImport = async () => {
    if (files.length === 0) return;

    setLoading(true);
    setError(null);
    setActiveStep(0);
    setStageProgress({
      parsing: 0,
      validating: 0,
      importing: 0,
    });

    try {
      // 创建一个组合的FormData
      const formData = new FormData();

      // 添加所有文件
      files.forEach(file => {
        formData.append('files[]', file);
      });
      formData.append('account_id', accountId);

      // 使用正确的 API 端点
      const response = await fetch(`${API_BASE_URL}/api/transactions/upload`, {
        method: 'POST',
        body: formData,
        // 不要设置 Content-Type，让浏览器自动设置正确的 multipart/form-data
        headers: {
          Accept: 'application/json',
        },
      });

      // 首先检查响应的内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        console.error('Invalid content type:', contentType);
        console.error('Response status:', response.status);
        console.error('Response text:', await response.text());
        throw new Error(
          `服务器返回了非JSON响应 (${response.status}): ${contentType}`
        );
      }

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || `导入失败 (${response.status})`);
      }

      // 开始轮询导入状态
      if (result.job_id) {
        checkImportStatus(result.job_id);
        setCurrentFile(files[0]); // 设置当前处理的文件
      } else {
        throw new Error('服务器没有返回任务ID');
      }
    } catch (error) {
      console.error('Import error:', error);
      setError(error.message || '导入过程中发生错误');
      setLoading(false);
    }
  };

  const startImport = () => {
    if (files.length > 0 && !loading) {
      setFailedRecords([]);
      setTotalImported(0);
      handleImport();
    }
  };

  const getCurrentProgress = () => {
    // 计算总体进度：解析(30%) + 验证(30%) + 导入(40%)
    let progress = 0;

    switch (activeStep) {
      case 0: // 解析
        progress = stageProgress.parsing * 0.3;
        break;
      case 1: // 验证
        progress = 30 + stageProgress.validating * 0.3;
        break;
      case 2: // 导入
        progress = 60 + stageProgress.importing * 0.4;
        break;
    }

    return Math.min(Math.round(progress), 100);
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>导入交易记录</DialogTitle>
      <DialogContent>
        <Box sx={{ pt: 2 }}>
          <input
            accept=".csv"
            style={{ display: 'none' }}
            id="csv-file-input"
            type="file"
            multiple
            onChange={handleFileChange}
          />
          <label htmlFor="csv-file-input">
            <Button
              variant="contained"
              component="span"
              startIcon={<CloudUploadIcon />}
              disabled={loading}
            >
              选择CSV文件
            </Button>
          </label>

          {files.length > 0 && (
            <Paper sx={{ mt: 2, p: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                待导入文件 ({files.length}):
              </Typography>
              <List dense>
                {files.map((file, index) => (
                  <ListItem
                    key={index}
                    secondaryAction={
                      !loading && (
                        <IconButton
                          edge="end"
                          aria-label="delete"
                          onClick={() => removeFile(index)}
                        >
                          <DeleteIcon />
                        </IconButton>
                      )
                    }
                  >
                    <ListItemText
                      primary={file.name}
                      secondary={
                        <>
                          {currentFile === file && (
                            <Box
                              sx={{
                                display: 'flex',
                                alignItems: 'center',
                                gap: 1,
                              }}
                            >
                              <Chip
                                label={`正在处理 (${getCurrentProgress()}%)`}
                                color="primary"
                                size="small"
                              />
                              <Typography
                                variant="caption"
                                color="text.secondary"
                              >
                                {steps[activeStep].label}
                              </Typography>
                            </Box>
                          )}
                          {currentFile !== file && index > currentFileIndex && (
                            <Typography
                              variant="caption"
                              color="text.secondary"
                            >
                              等待处理
                            </Typography>
                          )}
                          {currentFile !== file && index < currentFileIndex && (
                            <Chip
                              label="已完成"
                              color="success"
                              size="small"
                              variant="outlined"
                            />
                          )}
                        </>
                      }
                    />
                  </ListItem>
                ))}
              </List>
            </Paper>
          )}

          {loading && (
            <Box sx={{ mt: 3 }}>
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  导入进度
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {`正在处理: ${currentFile?.name || ''}`}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {steps[activeStep].description}
                </Typography>
              </Box>

              <LinearProgress
                variant="determinate"
                value={getCurrentProgress()}
                sx={{ height: 8, borderRadius: 2 }}
              />

              <Box
                sx={{ mt: 1, display: 'flex', justifyContent: 'space-between' }}
              >
                <Typography variant="caption" color="text.secondary">
                  {steps[activeStep].label}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {`${getCurrentProgress()}%`}
                </Typography>
              </Box>
            </Box>
          )}

          {error && (
            <Alert severity="error" sx={{ mt: 2 }}>
              {error}
            </Alert>
          )}
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          取消
        </Button>
        <Button
          onClick={startImport}
          variant="contained"
          disabled={files.length === 0 || loading}
        >
          {loading ? '导入中...' : '开始导入'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}

export default ImportTransactionsDialog;
