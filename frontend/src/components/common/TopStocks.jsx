import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Box,
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import { styled } from '@mui/material/styles';

const StyledListItem = styled(ListItem)(({ theme }) => ({
  borderRadius: theme.spacing(1),
  marginBottom: theme.spacing(1),
  backgroundColor: theme.palette.background.default,
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
}));

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
}));

function TopStocks() {
  const stocks = [
    { symbol: 'AAPL', name: '苹果公司', change: '+2.5%' },
    { symbol: 'GOOGL', name: '谷歌', change: '+1.8%' },
    { symbol: 'MSFT', name: '微软', change: '+3.2%' },
    { symbol: 'AMZN', name: '亚马逊', change: '+1.5%' },
  ];

  return (
    <StyledCard>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          热门股票
        </Typography>
        <List>
          {stocks.map(stock => (
            <StyledListItem key={stock.symbol}>
              <ListItemIcon>
                <TrendingUpIcon color="primary" />
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box display="flex" justifyContent="space-between">
                    <Typography variant="subtitle1" component="span">
                      {stock.symbol}
                    </Typography>
                    <Typography
                      variant="subtitle1"
                      component="span"
                      color="success.main"
                    >
                      {stock.change}
                    </Typography>
                  </Box>
                }
                secondary={stock.name}
              />
            </StyledListItem>
          ))}
        </List>
      </CardContent>
    </StyledCard>
  );
}

export default TopStocks;
