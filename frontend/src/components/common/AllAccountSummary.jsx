import React, { useState, useEffect } from 'react';
import {
  Card<PERSON>ontent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Box,
  Tooltip,
  IconButton,
  Collapse,
  Paper,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Upload as UploadIcon,
  Info as InfoIcon,
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
} from '@mui/icons-material';
import AddAccountDialog from '../forms/AddAccountDialog';
import ImportTransactionsDialog from '../forms/ImportTransactionsDialog';
import { API_BASE_URL, API_ENDPOINTS } from '../../constants/config';
import { get, post, del } from '../../services/api';
import toast from 'react-hot-toast';
import {
  PageCard,
  PrimaryButton,
  SecondaryButton,
  DestructiveButton,
  ModernTableHeadCell,
  ValueCell,
  LoadingBox,
  Styled<PERSON>lert,
  GradientTypography,
  CollapseContent,
} from '../../styles/common';

const StyledTableRow = styled(TableRow)(({ theme }) => ({
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  '&:nth-of-type(odd)': {
    background:
      'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%)',
  },
  '&:nth-of-type(even)': {
    background:
      'linear-gradient(135deg, rgba(248,250,252,0.6) 0%, rgba(255,255,255,0.8) 100%)',
  },
  '&:hover': {
    background:
      'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%) !important',
    transform: 'scale(1.002)',
  },
  '& td': {
    borderBottom: '1px solid rgba(224, 224, 224, 0.3)',
  },
  cursor: 'pointer',
}));

const ActionButton = ({ deleteButton, ...props }) => {
  if (deleteButton) {
    return <DestructiveButton {...props} />;
  }
  return <SecondaryButton {...props} />;
};

const HeaderCell = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(0.5),
}));

const DetailRow = styled(TableRow)(({ theme }) => ({
  backgroundColor: 'transparent',
  '& > td': {
    paddingBottom: 0,
    paddingTop: 0,
    border: 'none',
  },
}));

const DetailCell = styled(TableCell)(({ theme }) => ({
  padding: '0 20px 16px',
  backgroundColor: 'transparent',
  border: 'none',
}));

const DetailTableContainer = styled(TableContainer)({
  maxHeight: 440,
  borderRadius: '12px',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
});

const StockDetailTable = styled(Table)(({ theme }) => ({
  background:
    'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%)',
  backdropFilter: 'blur(10px)',
  '& th': {
    background:
      'linear-gradient(135deg, rgba(25, 118, 210, 0.08) 0%, rgba(21, 101, 192, 0.05) 100%), linear-gradient(135deg, rgb(255,255,255) 0%, rgb(248,250,252) 100%)',
    fontWeight: '700',
    color: '#1976D2',
    borderBottom: '2px solid rgba(25, 118, 210, 0.1)',
  },
  '& tbody tr': {
    transition: 'all 0.2s ease',
    '&:hover': {
      background:
        'linear-gradient(135deg, rgba(25, 118, 210, 0.04) 0%, rgba(21, 101, 192, 0.02) 100%)',
    },
    '&:nth-of-type(even)': {
      background: 'rgba(248, 250, 252, 0.3)',
    },
  },
}));

const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  borderRadius: '12px',
  overflow: 'hidden',
  background:
    'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.98) 100%)',
  backdropFilter: 'blur(10px)',
  boxShadow: '0 4px 16px rgba(0,0,0,0.08)',
  border: '1px solid rgba(255,255,255,0.2)',
}));

function Row({ account, onDelete, onAddStock, onImport }) {
  const [open, setOpen] = useState(false);
  const [stockDetails, setStockDetails] = useState([]);
  const [loading, setLoading] = useState(false);

  const accountId = account?.id || account?.account_id; // 兼容两种 ID 字段名

  const fetchStockDetails = async () => {
    if (!open) return;

    try {
      setLoading(true);
      const data = await get(`/api/analytics/holdings?account_id=${accountId}`);
      setStockDetails(data.holdings || []);
    } catch (error) {
      console.error('Error fetching stock details:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStockDetails();
  }, [open]);

  return (
    <>
      <StyledTableRow onClick={() => setOpen(!open)}>
        <TableCell sx={{ padding: '16px 20px' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <IconButton
              size="small"
              sx={{
                color: '#1976D2',
                transition: 'all 0.2s ease',
                '&:hover': {
                  background: 'rgba(25, 118, 210, 0.1)',
                  transform: 'scale(1.1)',
                },
              }}
            >
              {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
            </IconButton>
            <Typography
              variant="subtitle1"
              sx={{
                fontWeight: '700',
                fontSize: '1.1rem',
                background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
                backgroundClip: 'text',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
              }}
            >
              {account.name}
            </Typography>
          </Box>
        </TableCell>
        <ValueCell
          align="right"
          sx={{ padding: '16px 20px', fontWeight: '600' }}
        >
          ${(account.current_value || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.realized_gains}
          sx={{ padding: '16px 20px' }}
        >
          ${(account.realized_gains || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.total_gains}
          sx={{ padding: '16px 20px' }}
        >
          ${(account.total_gains || 0).toLocaleString()}
        </ValueCell>
        <ValueCell
          align="right"
          value={account.return_rate}
          sx={{ padding: '16px 20px' }}
        >
          {(account.return_rate || 0) > 0 ? '+' : ''}
          {((account.return_rate || 0) * 100).toFixed(2)}%
        </ValueCell>
        <TableCell
          align="right"
          onClick={e => e.stopPropagation()}
          sx={{ padding: '12px 20px' }}
        >
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-end',
              gap: 1.5,
            }}
          >
            <ActionButton
              size="small"
              variant="outlined"
              onClick={() => onImport(accountId)}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <UploadIcon fontSize="small" />
                <span>导入交易</span>
              </Box>
            </ActionButton>
            <ActionButton
              size="small"
              variant="outlined"
              deleteButton={true}
              onClick={e => {
                e.stopPropagation();
                if (accountId) {
                  onDelete(accountId, e);
                } else {
                  console.error('Account ID is missing:', account);
                }
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <DeleteIcon fontSize="small" />
                <span>删除</span>
              </Box>
            </ActionButton>
          </Box>
        </TableCell>
      </StyledTableRow>
      <DetailRow key={`detail-${accountId}`}>
        <DetailCell colSpan={6}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <CollapseContent>
              <Typography
                variant="h6"
                gutterBottom
                component="div"
                sx={{
                  fontWeight: '700',
                  color: '#1976D2',
                  mb: 2,
                }}
              >
                持仓明细
              </Typography>
              {loading ? (
                <LoadingBox>
                  <CircularProgress sx={{ color: '#1976D2' }} />
                </LoadingBox>
              ) : (
                <DetailTableContainer>
                  <StockDetailTable size="small" stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>股票代码</TableCell>
                        <TableCell align="right">持仓数量</TableCell>
                        <TableCell align="right">每股成本 (USD)</TableCell>
                        <TableCell align="right">当前价格 (USD)</TableCell>
                        <TableCell align="right">总成本 (USD)</TableCell>
                        <TableCell align="right">当前市值 (USD)</TableCell>
                        <TableCell align="right">收益 (USD)</TableCell>
                        <TableCell align="right">收益率</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {stockDetails.map((stock, index) => (
                        <TableRow
                          key={`${accountId}-${stock.symbol}`}
                          sx={{
                            animation: `slideInDetail 0.5s ease-out ${index * 0.05}s both`,
                            '@keyframes slideInDetail': {
                              '0%': {
                                opacity: 0,
                                transform: 'translateX(-20px)',
                              },
                              '100%': {
                                opacity: 1,
                                transform: 'translateX(0)',
                              },
                            },
                          }}
                        >
                          <TableCell
                            component="th"
                            scope="row"
                            sx={{ fontWeight: '600', color: '#1976D2' }}
                          >
                            {stock.symbol}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: '500' }}>
                            {(stock.quantity || 0).toLocaleString()}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: '500' }}>
                            $
                            {(stock.cost_per_share || 0).toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: '500' }}>
                            $
                            {(stock.current_price || 0).toLocaleString(
                              undefined,
                              {
                                minimumFractionDigits: 2,
                                maximumFractionDigits: 2,
                              }
                            )}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: '500' }}>
                            $
                            {(stock.cost || 0).toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </TableCell>
                          <TableCell align="right" sx={{ fontWeight: '500' }}>
                            $
                            {(stock.value || 0).toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </TableCell>
                          <ValueCell align="right" value={stock.gains}>
                            $
                            {(stock.gains || 0).toLocaleString(undefined, {
                              minimumFractionDigits: 2,
                              maximumFractionDigits: 2,
                            })}
                          </ValueCell>
                          <ValueCell align="right" value={stock.return_rate}>
                            {(stock.return_rate || 0) > 0 ? '+' : ''}
                            {((stock.return_rate || 0) * 100).toFixed(2)}%
                          </ValueCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </StockDetailTable>
                </DetailTableContainer>
              )}
            </CollapseContent>
          </Collapse>
        </DetailCell>
      </DetailRow>
    </>
  );
}

function AllAccountSummary({ onRefresh }) {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [openAddAccount, setOpenAddAccount] = useState(false);
  const [openImport, setOpenImport] = useState(false);
  const [selectedAccountId, setSelectedAccountId] = useState(null);
  const [refreshCounter, setRefreshCounter] = useState(0);

  const fetchAccounts = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await get(API_ENDPOINTS.OVERVIEW);
      setAccounts(data.account_breakdown || []);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      setError('获取账户数据失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('触发账户数据刷新...');
    fetchAccounts();
  }, [refreshCounter]);

  const handleDelete = async (accountId, event) => {
    // 确保事件处理
    if (event) {
      event.stopPropagation();
    }

    // 验证 accountId
    if (!accountId) {
      const error = '无效的账户ID';
      console.error(error, { accountId });
      setError(error);
      return;
    }

    console.log('Attempting to delete account:', accountId);

    if (!window.confirm('确定要删除这个账户吗？')) {
      console.log('Delete cancelled by user');
      return;
    }

    try {
      console.log('Sending delete request for account:', accountId);
      await del(`${API_ENDPOINTS.ACCOUNTS}/${accountId}`);

      console.log('Successfully deleted account:', accountId);
      toast.success('账户删除成功');
      // 使用 id 或 account_id 进行过滤
      setAccounts(prev =>
        prev.filter(acc => (acc.id || acc.account_id) !== accountId)
      );

      if (onRefresh) {
        console.log('Triggering refresh after delete');
        onRefresh();
      }
    } catch (error) {
      const errorMessage = '删除账户失败';
      console.error(errorMessage, { accountId, error });
      toast.error(`${errorMessage}: ${error.message}`);
      setError(`${errorMessage}: ${error.message}`);
    }
  };

  const handleCreateAccount = async accountData => {
    try {
      await post(API_ENDPOINTS.ACCOUNTS, accountData);
      toast.success('账户创建成功');
      setOpenAddAccount(false);
      fetchAccounts();
      if (onRefresh) onRefresh();
    } catch (error) {
      console.error('Error creating account:', error);
      toast.error(`创建账户失败: ${error.message}`);
      throw error;
    }
  };

  return (
    <>
      <PageCard>
        <CardContent sx={{ p: 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 3,
            }}
          >
            <GradientTypography>账户汇总</GradientTypography>
            <Box>
              <PrimaryButton
                variant="contained"
                startIcon={<AddIcon />}
                onClick={() => setOpenAddAccount(true)}
              >
                新增账户
              </PrimaryButton>
            </Box>
          </Box>
          {loading ? (
            <LoadingBox>
              <CircularProgress sx={{ color: '#1976D2' }} />
            </LoadingBox>
          ) : error ? (
            <StyledAlert severity="error">{error}</StyledAlert>
          ) : (
            <StyledTableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <ModernTableHeadCell>
                      <HeaderCell>
                        账户名称
                        <Tooltip title="账户的唯一标识名称" arrow>
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        当前市值 (USD)
                        <Tooltip
                          title="所有持仓股票的当前市值总和 = Σ(每支股票的持仓数量 × 当前价格)"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        已实现收益 (USD)
                        <Tooltip
                          title="已经卖出股票的收益总和 = Σ(卖出价格 - 买入价格) × 卖出数量"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        总收益 (USD)
                        <Tooltip
                          title="当前持仓的未实现总收益 + 历史已实现总收益"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      <HeaderCell sx={{ justifyContent: 'flex-end' }}>
                        持仓收益率
                        <Tooltip
                          title="当前持仓的未实现总收益 / 当前持仓的总成本 × 100%"
                          arrow
                        >
                          <IconButton size="small" sx={{ color: '#1976D2' }}>
                            <InfoIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </HeaderCell>
                    </ModernTableHeadCell>
                    <ModernTableHeadCell align="right">
                      操作
                    </ModernTableHeadCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {accounts.map(account => (
                    <Row
                      key={account.account_id}
                      account={account}
                      onDelete={handleDelete}
                      onImport={accountId => {
                        setSelectedAccountId(accountId);
                        setOpenImport(true);
                      }}
                    />
                  ))}
                </TableBody>
              </Table>
            </StyledTableContainer>
          )}
        </CardContent>
      </PageCard>

      <AddAccountDialog
        open={openAddAccount}
        onClose={() => setOpenAddAccount(false)}
        onSubmit={handleCreateAccount}
      />

      <ImportTransactionsDialog
        open={openImport}
        onClose={() => {
          setOpenImport(false);
          setSelectedAccountId(null);
        }}
        onSuccess={async importedCount => {
          const message = `导入完成: 共导入 ${importedCount} 条记录`;
          console.log(message);
          toast.success(message);
          setRefreshCounter(prev => prev + 1);
          await fetchAccounts();
          if (onRefresh) {
            console.log('调用父组件刷新方法...');
            onRefresh();
          }
          console.log('所有刷新完成');
        }}
        accountId={selectedAccountId}
      />
    </>
  );
}

export default AllAccountSummary;
