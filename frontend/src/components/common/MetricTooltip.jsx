import React from 'react';
import { IconButton, Tooltip } from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';

const MetricTooltip = ({ description }) => {
  if (!description) {
    return null;
  }

  return (
    <Tooltip
      title={<span style={{ whiteSpace: 'pre-line' }}>{description}</span>}
      arrow
      placement="top"
    >
      <IconButton
        size="small"
        sx={{
          ml: 0.5,
          '&:hover': {
            backgroundColor: 'rgba(0, 0, 0, 0.04)',
          },
        }}
      >
        <InfoIcon fontSize="small" />
      </IconButton>
    </Tooltip>
  );
};

export default MetricTooltip;
