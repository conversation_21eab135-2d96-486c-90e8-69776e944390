import React, { useState, useEffect } from 'react';
import {
  Card<PERSON>ontent,
  Typography,
  Box,
  CircularProgress,
  List,
  ListItem,
  ToggleButtonGroup,
  ToggleButton,
} from '@mui/material';
import { Star as StarIcon } from '@mui/icons-material';
import {
  PageCard,
  GradientTypography,
  LoadingBox,
  StyledAlert,
  EmptyStateBox,
} from '../../styles/common';
import { API_BASE_URL } from '../../constants/config';

const StockPerformanceBar = ({ value }) => {
  const isPositive = value >= 0;
  const positiveGradient = 'linear-gradient(90deg, #4CAF50 0%, #2E7D32 100%)';
  const negativeGradient = 'linear-gradient(90deg, #F44336 0%, #C62828 100%)';
  const background = isPositive ? positiveGradient : negativeGradient;
  const color = isPositive ? '#2E7D32' : '#C62828';
  const width = Math.min(Math.abs(value) * 100, 100);

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
      <Box sx={{ width: '65px', textAlign: 'right', mr: 2 }}>
        <Typography
          variant="body1"
          sx={{
            color,
            fontWeight: '700',
            fontSize: '0.95rem',
            textShadow: '0 1px 2px rgba(0,0,0,0.1)',
          }}
        >
          {`${(value * 100).toFixed(1)}%`}
        </Typography>
      </Box>
      <Box
        sx={{
          flexGrow: 1,
          height: '14px',
          backgroundColor: '#F5F5F5',
          borderRadius: '8px',
          overflow: 'hidden',
          boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            width: `${width}%`,
            height: '100%',
            background: background,
            transition: 'width 0.8s cubic-bezier(0.4, 0, 0.2, 1)',
            borderRadius: '8px',
            position: 'relative',
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '50%',
              background:
                'linear-gradient(180deg, rgba(255,255,255,0.3) 0%, transparent 100%)',
              borderRadius: '8px 8px 0 0',
            },
          }}
        />
      </Box>
    </Box>
  );
};

const StockListItem = ({ stock, index, isTopPerformer }) => (
  <ListItem
    sx={{
      py: 2,
      px: 0,
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      borderRadius: '12px',
      mb: 1,
      background: isTopPerformer
        ? 'linear-gradient(135deg, rgba(255,215,0,0.1) 0%, rgba(255,193,7,0.05) 50%, rgba(255,255,255,0.8) 100%)'
        : 'linear-gradient(135deg, rgba(255,255,255,0.8) 0%, rgba(248,250,252,0.9) 100%)',
      backdropFilter: 'blur(8px)',
      border: isTopPerformer
        ? '1px solid rgba(255,215,0,0.3)'
        : '1px solid rgba(255,255,255,0.2)',
      boxShadow: isTopPerformer
        ? '0 2px 8px rgba(255,215,0,0.15), 0 4px 16px rgba(0,0,0,0.06)'
        : '0 2px 8px rgba(0,0,0,0.06)',
      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
      animation: `slideIn 0.5s ease-out ${index * 0.1}s both`,
      '@keyframes slideIn': {
        '0%': {
          opacity: 0,
          transform: 'translateY(20px)',
        },
        '100%': {
          opacity: 1,
          transform: 'translateY(0)',
        },
      },
      '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: isTopPerformer
          ? '0 4px 16px rgba(255,215,0,0.2), 0 6px 20px rgba(0,0,0,0.12)'
          : '0 4px 16px rgba(0,0,0,0.12)',
        background: isTopPerformer
          ? 'linear-gradient(135deg, rgba(255,215,0,0.15) 0%, rgba(255,193,7,0.08) 50%, rgba(255,255,255,0.95) 100%)'
          : 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,1) 100%)',
      },
    }}
  >
    <Box
      sx={{ flexBasis: '30%', pl: 2, display: 'flex', alignItems: 'center' }}
    >
      {isTopPerformer && (
        <StarIcon
          sx={{
            color: '#FFD700',
            fontSize: '1.2rem',
            mr: 1,
            filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.2))',
            animation: 'sparkle 2s ease-in-out infinite',
            '@keyframes sparkle': {
              '0%, 100%': { transform: 'scale(1)' },
              '50%': { transform: 'scale(1.1)' },
            },
          }}
        />
      )}
      <Box>
        <Typography
          variant="h6"
          component="div"
          sx={{
            fontWeight: '700',
            lineHeight: 1.2,
            background: 'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            fontSize: '1.1rem',
          }}
        >
          {stock.symbol}
        </Typography>
        <Typography
          variant="body2"
          sx={{
            color: '#64748B',
            fontWeight: '500',
            mt: 0.5,
          }}
        >
          {`来自: ${stock.account_name}`}
        </Typography>
      </Box>
    </Box>
    <Box sx={{ flexBasis: '65%', pr: 2 }}>
      <StockPerformanceBar value={stock.return_rate} />
    </Box>
  </ListItem>
);

const StockPerformance = ({ refreshKey }) => {
  const [data, setData] = useState({ top: [], bottom: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [view, setView] = useState('top'); // 'top' or 'bottom'

  const handleViewChange = (event, newView) => {
    if (newView !== null) {
      setView(newView);
    }
  };

  useEffect(() => {
    const fetchHoldings = async () => {
      setLoading(true);
      setError(null);
      try {
        const response = await fetch(`${API_BASE_URL}/api/analytics/holdings`);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        const result = await response.json();
        const holdings = (result.holdings || []).filter(
          h => h.return_rate !== null && isFinite(h.return_rate)
        );

        holdings.sort((a, b) => b.return_rate - a.return_rate);

        const top5 = holdings.slice(0, 5);
        const bottom5 = holdings
          .slice(-5)
          .sort((a, b) => a.return_rate - b.return_rate);

        setData({ top: top5, bottom: bottom5 });
      } catch (e) {
        console.error('Error fetching holdings data:', e);
        setError(e.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHoldings();
  }, [refreshKey]);

  if (loading) {
    return (
      <PageCard>
        <LoadingBox>
          <CircularProgress sx={{ color: '#1976D2' }} />
        </LoadingBox>
      </PageCard>
    );
  }

  if (error) {
    return (
      <PageCard>
        <CardContent>
          <StyledAlert severity="error">
            加载持仓数据时出错: {error}
          </StyledAlert>
        </CardContent>
      </PageCard>
    );
  }

  const hasData = data.top.length > 0 || data.bottom.length > 0;

  return (
    <PageCard>
      <CardContent sx={{ p: 3, '&:last-child': { pb: 3 } }}>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 3,
          }}
        >
          <GradientTypography variant="h6">投资回报率</GradientTypography>
          <ToggleButtonGroup
            value={view}
            exclusive
            onChange={handleViewChange}
            size="small"
            sx={{
              '& .MuiToggleButton-root': {
                borderRadius: '12px',
                fontWeight: '600',
                textTransform: 'none',
                px: 2.5,
                py: 1,
                border: '1px solid rgba(25, 118, 210, 0.3)',
                color: '#1976D2',
                transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                '&:hover': {
                  background: 'rgba(25, 118, 210, 0.08)',
                  transform: 'translateY(-1px)',
                },
                '&.Mui-selected': {
                  background:
                    'linear-gradient(135deg, #1976D2 0%, #1565C0 100%)',
                  color: 'white',
                  boxShadow: '0 4px 12px rgba(25, 118, 210, 0.3)',
                  '&:hover': {
                    background:
                      'linear-gradient(135deg, #1565C0 0%, #0D47A1 100%)',
                  },
                },
              },
            }}
          >
            <ToggleButton value="top">表现最佳</ToggleButton>
            <ToggleButton value="bottom">表现最差</ToggleButton>
          </ToggleButtonGroup>
        </Box>
        {!hasData ? (
          <EmptyStateBox sx={{ height: 'calc(100% - 80px)' }}>
            <Typography
              variant="body1"
              sx={{
                color: '#64748B',
                fontWeight: '500',
                textAlign: 'center',
              }}
            >
              暂无股票表现数据
            </Typography>
          </EmptyStateBox>
        ) : (
          <List dense sx={{ p: 0 }}>
            {view === 'top' &&
              data.top.map((stock, index) => (
                <StockListItem
                  key={`${stock.symbol}-top-${index}`}
                  stock={stock}
                  index={index}
                  isTopPerformer={index === 0}
                />
              ))}
            {view === 'bottom' &&
              data.bottom.map((stock, index) => (
                <StockListItem
                  key={`${stock.symbol}-bottom-${index}`}
                  stock={stock}
                  index={index}
                  isTopPerformer={false}
                />
              ))}
          </List>
        )}
      </CardContent>
    </PageCard>
  );
};

export default StockPerformance;
