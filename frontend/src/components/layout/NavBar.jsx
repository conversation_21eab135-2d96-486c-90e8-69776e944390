import React from 'react';
import {
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Button,
  Box,
  Container,
  useMediaQuery,
  useTheme,
  IconButton,
  Drawer,
  List,
  ListItemText,
  ListItemButton,
} from '@mui/material';
import {
  Home,
  AccountBalanceWallet,
  TrendingUp,
  Info,
  Menu,
  Close,
} from '@mui/icons-material';
import { Link, useLocation } from 'react-router-dom';
import { styled } from '@mui/material/styles';
import { useState } from 'react';

const StyledAppBar = styled(AppBar)(({ theme }) => ({
  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  backdropFilter: 'blur(10px)',
  boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
  border: '1px solid rgba(255, 255, 255, 0.18)',
}));

const StyledButton = styled(But<PERSON>)(({ theme, active }) => ({
  margin: theme.spacing(0, 0.5),
  padding: theme.spacing(1, 2),
  color: active ? '#ffffff' : 'rgba(255, 255, 255, 0.8)',
  position: 'relative',
  borderRadius: '25px',
  textTransform: 'none',
  fontWeight: active ? 600 : 500,
  fontSize: '0.95rem',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  background: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',
  backdropFilter: active ? 'blur(10px)' : 'none',
  border: active
    ? '1px solid rgba(255, 255, 255, 0.2)'
    : '1px solid transparent',

  '&:hover': {
    background: 'rgba(255, 255, 255, 0.1)',
    backdropFilter: 'blur(10px)',
    border: '1px solid rgba(255, 255, 255, 0.2)',
    color: '#ffffff',
    transform: 'translateY(-2px)',
    boxShadow: '0 4px 20px 0 rgba(31, 38, 135, 0.4)',
  },

  '& .MuiButton-startIcon': {
    marginRight: theme.spacing(1),
    transition: 'transform 0.3s ease',
  },

  '&:hover .MuiButton-startIcon': {
    transform: 'scale(1.1)',
  },
}));

const Logo = styled(Typography)(({ theme }) => ({
  fontWeight: 700,
  fontSize: '1.5rem',
  background: 'linear-gradient(45deg, #ffffff 30%, #f0f0f0 90%)',
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  textShadow: '0 2px 4px rgba(0,0,0,0.1)',
  letterSpacing: '0.5px',
}));

const MobileDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    color: 'white',
  },
}));

const MobileListItem = styled(ListItemButton)(({ theme, active }) => ({
  margin: theme.spacing(0.5, 2),
  borderRadius: '15px',
  background: active ? 'rgba(255, 255, 255, 0.15)' : 'transparent',
  '&:hover': {
    background: 'rgba(255, 255, 255, 0.1)',
  },
}));

const NavBar = () => {
  const location = useLocation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileOpen, setMobileOpen] = useState(false);

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const menuItems = [
    { path: '/', label: '主页', icon: <Home /> },
    { path: '/accounts', label: '账户', icon: <AccountBalanceWallet /> },
    { path: '/strategies', label: '策略', icon: <TrendingUp /> },
    { path: '/reference', label: '信息', icon: <Info /> },
  ];

  const drawer = (
    <Box sx={{ pt: 2 }}>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          px: 2,
          pb: 2,
        }}
      >
        <Logo variant="h6">股票管理系统</Logo>
        <IconButton onClick={handleDrawerToggle} sx={{ color: 'white' }}>
          <Close />
        </IconButton>
      </Box>
      <List>
        {menuItems.map(item => (
          <MobileListItem
            key={item.path}
            component={Link}
            to={item.path}
            active={location.pathname === item.path ? 1 : 0}
            onClick={handleDrawerToggle}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Box sx={{ mr: 2, display: 'flex', alignItems: 'center' }}>
                {item.icon}
              </Box>
              <ListItemText
                primary={item.label}
                primaryTypographyProps={{
                  fontWeight: location.pathname === item.path ? 600 : 500,
                }}
              />
            </Box>
          </MobileListItem>
        ))}
      </List>
    </Box>
  );

  return (
    <>
      <StyledAppBar position="static" elevation={0}>
        <Container maxWidth="xl">
          <Toolbar sx={{ justifyContent: 'space-between', py: 1 }}>
            <Logo variant="h6">股票管理系统</Logo>

            {isMobile ? (
              <IconButton
                color="inherit"
                edge="start"
                onClick={handleDrawerToggle}
                sx={{
                  color: 'white',
                  '&:hover': {
                    background: 'rgba(255, 255, 255, 0.1)',
                  },
                }}
              >
                <Menu />
              </IconButton>
            ) : (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {menuItems.map(item => (
                  <StyledButton
                    key={item.path}
                    component={Link}
                    to={item.path}
                    active={location.pathname === item.path ? 1 : 0}
                    startIcon={item.icon}
                  >
                    {item.label}
                  </StyledButton>
                ))}
              </Box>
            )}
          </Toolbar>
        </Container>
      </StyledAppBar>

      <MobileDrawer
        variant="temporary"
        open={mobileOpen}
        onClose={handleDrawerToggle}
        ModalProps={{
          keepMounted: true,
        }}
      >
        {drawer}
      </MobileDrawer>
    </>
  );
};

export default NavBar;
