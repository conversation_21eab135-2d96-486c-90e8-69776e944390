import React, { createContext, useState } from 'react';

// Create a context for refreshing data
export const RefreshContext = createContext({
  refreshFlag: false,
  setRefreshFlag: () => {},
});

// Provider component to wrap the app and provide the refresh flag state
export const RefreshProvider = ({ children }) => {
  const [refreshFlag, setRefreshFlag] = useState(false);
  return (
    <RefreshContext.Provider value={{ refreshFlag, setRefreshFlag }}>
      {children}
    </RefreshContext.Provider>
  );
};

export default RefreshContext;
