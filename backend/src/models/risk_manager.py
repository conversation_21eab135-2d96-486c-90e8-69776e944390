import traceback
from typing import Dict, List

import numpy as np
import pandas as pd

from ..utils.logger import get_logger
from ..utils.technical_indicators import calculate_adx
from .strategy import Stock

logger = get_logger()


class RiskManager:
    def __init__(self, config: Dict = None):
        self.config = config or {}
        # 最大持仓比例：单个持仓占总资产的最大比例
        self.max_position_size = self.config.get("max_position_size", 0.2)
        # 最大组合波动率：投资组合允许的最大年化波动率
        self.max_portfolio_vol = self.config.get("max_portfolio_vol", 0.25)

    def calculate_position_size(self, df: pd.DataFrame, capital: float, signal_strength: float) -> float:
        """Calculate optimal position size based on multiple factors"""
        try:
            # Get regime-specific parameters
            params = self.position_sizing_model()

            # Calculate volatility metrics
            returns = df["Close"].pct_change()
            current_vol = returns.rolling(20).std() * np.sqrt(252)
            vol_ratio = current_vol.iloc[-1] / current_vol.rolling(60).mean().iloc[-1]

            # Calculate trend strength
            trend_strength = self._calculate_trend_strength(df)

            # Kelly Criterion calculation
            win_rate = self._calculate_win_rate(df)
            avg_win = self._calculate_avg_win(df)
            avg_loss = self._calculate_avg_loss(df)
            kelly_fraction = win_rate - ((1 - win_rate) / (avg_win / abs(avg_loss)))
            kelly_fraction = max(0, min(kelly_fraction, 0.5))  # Cap at 50%

            # Combine all factors
            base_size = params["base_size"]
            vol_adjustment = np.exp(-params["vol_scalar"] * vol_ratio)
            momentum_adjustment = params["momentum_scalar"] * abs(signal_strength)

            # Calculate final position size
            position_size = base_size * kelly_fraction * vol_adjustment * momentum_adjustment * trend_strength

            # Apply limits
            position_size = min(position_size, self.max_position_size)
            max_position_value = capital * position_size

            return max_position_value

        except Exception as e:
            logger.error(f"Error calculating position size: {str(e)}")
            return 0.0

    def _calculate_trend_strength(self, df: pd.DataFrame) -> float:
        """Calculate trend strength using multiple indicators"""
        try:
            # ADX for trend strength
            adx = calculate_adx(df)
            adx_factor = min(adx.iloc[-1] / 25.0, 1.5)

            # Moving average alignment
            ma_20 = df["Close"].rolling(20).mean()
            ma_50 = df["Close"].rolling(50).mean()
            ma_200 = df["Close"].rolling(200).mean()

            ma_alignment = 1.0
            if ma_20.iloc[-1] > ma_50.iloc[-1] > ma_200.iloc[-1]:
                ma_alignment = 1.2  # Strong uptrend
            elif ma_20.iloc[-1] < ma_50.iloc[-1] < ma_200.iloc[-1]:
                ma_alignment = 0.8  # Strong downtrend

            return adx_factor * ma_alignment

        except Exception as e:
            logger.error(f"Error calculating trend strength: {str(e)}")
            return 1.0

    def _calculate_win_rate(self, df: pd.DataFrame, lookback: int = 100) -> float:
        """Calculate historical win rate"""
        try:
            returns = df["Close"].pct_change()
            wins = (returns > 0).rolling(lookback).mean().iloc[-1]
            return max(0.1, min(wins, 0.9))  # Bound between 10% and 90%
        except Exception as e:
            logger.error(f"Error calculating win rate: {str(e)}")
            return 0.5

    def _calculate_avg_win(self, df: pd.DataFrame, lookback: int = 100) -> float:
        """Calculate average winning trade"""
        try:
            returns = df["Close"].pct_change()
            positive_returns = returns[returns > 0]
            if len(positive_returns) > 0:
                avg_win = positive_returns.tail(lookback).mean()
                return max(0.001, avg_win)
            return 0.01
        except Exception as e:
            logger.error(f"Error calculating average win: {str(e)}")
            return 0.01

    def _calculate_avg_loss(self, df: pd.DataFrame, lookback: int = 100) -> float:
        """Calculate average losing trade"""
        try:
            returns = df["Close"].pct_change()
            negative_returns = returns[returns < 0]
            if len(negative_returns) > 0:
                avg_loss = negative_returns.tail(lookback).mean()
                return min(-0.001, avg_loss)
            return -0.01
        except Exception as e:
            logger.error(f"Error calculating average loss: {str(e)}")
            return -0.01

    @staticmethod
    def calculate_portfolio_risk(
        positions: List[Stock], market_data: Dict[str, pd.DataFrame], lookback_window: int = 252
    ) -> Dict:
        """
        计算投资组合风险指标
        """
        try:
            # 提取持仓价值和权重
            total_value = sum(pos.current_value for pos in positions)
            if total_value <= 0:
                logger.warning("投资组合总价值为0或负数")
                return {
                    "portfolio_var": 0.0,
                    "portfolio_vol": 0.0,
                    "max_drawdown": 0.0,
                    "sharpe_ratio": 0.0,
                    "risk_level": "LOW",
                }

            weights = np.array([pos.current_value / total_value for pos in positions])

            # 计算每个持仓的收益率
            returns_data = []
            valid_positions = []  # 跟踪有效的持仓
            valid_weights = []  # 跟踪有效的权重

            # 动态调整回看窗口
            min_data_length = float("inf")
            for i, pos in enumerate(positions):
                if pos.symbol in market_data:
                    df = market_data[pos.symbol].copy()
                    df.columns = df.columns.str.title()
                    if "Close" in df.columns and len(df) >= 2:
                        returns = df["Close"].pct_change().fillna(0)
                        min_data_length = min(min_data_length, len(returns))
                        if len(returns) >= 2:  # 至少需要2个数据点
                            returns_data.append(returns)
                            valid_positions.append(pos)
                            valid_weights.append(weights[i])

            # 如果数据量不足，调整回看窗口
            if min_data_length < lookback_window:
                adjusted_window = max(20, min_data_length)  # 至少需要20个数据点
                logger.info(f"调整回看窗口从 {lookback_window} 到 {adjusted_window}")
                lookback_window = adjusted_window

            if not returns_data:
                logger.warning("没有足够的历史数据来计算风险指标")
                # 使用简单波动率计算
                simple_vol = np.mean([getattr(pos, "volatility", 0.15) for pos in positions]) if positions else 0.15
                return {
                    "portfolio_var": float(simple_vol**2),
                    "portfolio_vol": float(simple_vol),
                    "max_drawdown": 0.0,
                    "sharpe_ratio": 0.0,
                    "risk_level": "MEDIUM" if simple_vol > 0.15 else "LOW",
                }

            # 确保所有收益率序列长度相同
            min_length = min(len(r) for r in returns_data)
            returns_data = [r[-min_length:].values for r in returns_data]

            # 转换为numpy数组并规范化权重
            returns_matrix = np.array(returns_data).T  # 转置以获得正确的维度
            valid_weights = np.array(valid_weights)
            valid_weights = valid_weights / np.sum(valid_weights)  # 重新规范化权重

            # 计算协方差矩阵
            cov_matrix = np.cov(returns_matrix.T)

            # 计算组合方差和波动率
            try:
                portfolio_var = np.dot(valid_weights.T, np.dot(cov_matrix, valid_weights))
                portfolio_vol = np.sqrt(portfolio_var * 252)  # 年化
            except Exception as e:
                logger.error(f"计算组合波动率时出错: {str(e)}")
                portfolio_var = 0.0
                portfolio_vol = 0.0

            # 计算组合收益率
            try:
                portfolio_returns = np.sum(returns_matrix * valid_weights, axis=1)
            except Exception as e:
                logger.error(f"计算组合收益率时出错: {str(e)}")
                portfolio_returns = np.zeros(min_length)

            # 计算最大回撤
            try:
                cumulative_returns = (1 + portfolio_returns).cumprod()
                rolling_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (rolling_max - cumulative_returns) / rolling_max
                max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0.0
            except Exception as e:
                logger.error(f"计算最大回撤时出错: {str(e)}")
                max_drawdown = 0.0

            # 计算夏普比率（假设无风险利率为2%）
            try:
                excess_returns = portfolio_returns.mean() * 252 - 0.02
                returns_std = portfolio_returns.std() * np.sqrt(252)
                sharpe_ratio = excess_returns / (returns_std + 1e-10)  # 添加小值避免除零
            except Exception as e:
                logger.error(f"计算夏普比率时出错: {str(e)}")
                sharpe_ratio = 0.0

            # 确保所有指标都是有效的数值
            metrics = {
                "portfolio_var": float(portfolio_var) if not np.isnan(portfolio_var) else 0.0,
                "portfolio_vol": float(portfolio_vol) if not np.isnan(portfolio_vol) else 0.0,
                "max_drawdown": float(max_drawdown) if not np.isnan(max_drawdown) else 0.0,
                "sharpe_ratio": float(sharpe_ratio) if not np.isnan(sharpe_ratio) else 0.0,
            }

            # 根据波动率确定风险等级
            # 标准：
            # HIGH: 波动率 > 25%
            # MEDIUM: 15% < 波动率 <= 25%
            # LOW: 波动率 <= 15%
            if metrics["portfolio_vol"] > 0.25:  # 25%
                metrics["risk_level"] = "HIGH"
            elif metrics["portfolio_vol"] > 0.15:  # 15%
                metrics["risk_level"] = "MEDIUM"
            else:
                metrics["risk_level"] = "LOW"

            return metrics

        except Exception as e:
            logger.error(f"计算组合风险指标时发生错误: {str(e)}\n{traceback.format_exc()}")
            return {"portfolio_var": 0.0, "portfolio_vol": 0.0, "max_drawdown": 0.0, "sharpe_ratio": 0.0, "risk_level": "LOW"}
