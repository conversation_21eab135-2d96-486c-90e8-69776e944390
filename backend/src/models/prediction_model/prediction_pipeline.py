import os
from typing import List

from ...utils.logger import get_logger  # Relative import for logger

# prediction_pipeline.py is in backend/models/prediction_model/
# And model.py is in the same directory (backend/models/prediction_model/model.py)
from .model import run_training_pipeline as process_ticker_model_and_get_predictions  # Relative import for model

logger = get_logger()

# Model directory, consistent with model.py
# This is used to check for existing model checkpoints.
# Resolve MODEL_DIR to be absolute path from the backend directory for robustness
BACKEND_DIR = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)  # Goes up three levels to backend root
MODEL_DIR_NAME = "data/models/prediction_model_data"  # Updated path to match where models are actually stored
MODEL_STORAGE_ROOT = os.path.join(BACKEND_DIR, MODEL_DIR_NAME)
os.makedirs(MODEL_STORAGE_ROOT, exist_ok=True)


def run_training_pipeline_for_tickers(ticker_symbols_to_process: List[str] = None, force_retrain: bool = False):
    """Wrapper to run training pipeline for a list of tickers."""
    logger.info(f"--- Starting Model Training Pipeline (Wrapper) for tickers: {ticker_symbols_to_process} ---")
    if not ticker_symbols_to_process:
        logger.warning("Model Training Pipeline (Wrapper): No tickers provided.")
        return

    for ticker in ticker_symbols_to_process:
        logger.info(f"--- Processing ticker: {ticker} for training/update (Force retrain: {force_retrain}) ---")
        try:
            # Pass the force_retrain flag and the correct model storage path to the underlying function
            # Assuming process_ticker_model_and_get_predictions (model.run_training_pipeline) will be updated
            # to accept force_retrain and model_storage_root
            process_ticker_model_and_get_predictions(
                ticker=ticker, model_storage_root=MODEL_STORAGE_ROOT, force_retrain=force_retrain
            )
            logger.info(f"Successfully processed ticker: {ticker}")
        except Exception as e:
            logger.error(f"Failed to process ticker {ticker} in training pipeline. Error: {e}", exc_info=True)
        logger.info(f"--- Finished processing ticker: {ticker} for training/update ---")
    logger.info("--- Model Training Pipeline (Wrapper) Finished ---")


def get_future_predictions_for_ticker(ticker_symbol: str) -> dict:
    """
    Loads a pre-trained model for the ticker and returns its future predicted price data points.
    Does NOT trigger training if the model is not found; relies on a separate daily job for training.

    Args:
        ticker_symbol (str): The stock ticker symbol (e.g., 'AAPL').

    Returns:
        dict: A dictionary containing the entire predictions_output.
              Returns an empty dictionary if the model is not found or predictions cannot be generated.
    """
    logger.info(f"--- Requesting future predictions for ticker: {ticker_symbol} (using pre-trained model) ---")

    # Construct path to where this specific ticker's model should be stored
    ticker_specific_model_dir = os.path.join(MODEL_STORAGE_ROOT, ticker_symbol)
    checkpoint_model_path = os.path.join(ticker_specific_model_dir, f"{ticker_symbol}_transformer_checkpoint.pth")
    checkpoint_params_path = os.path.join(ticker_specific_model_dir, f"{ticker_symbol}_transformer_checkpoint_params.json")

    if not (os.path.exists(checkpoint_model_path) and os.path.exists(checkpoint_params_path)):
        logger.warning(
            f"Model checkpoint not found for {ticker_symbol} at {ticker_specific_model_dir}. "
            f"Predictions cannot be generated on-demand without a pre-trained model. "
            f"The daily training job should create this."
        )
        return {}  # Return empty dictionary, do not train on-demand

    logger.info(f"Model checkpoint found for {ticker_symbol}. Proceeding to generate predictions.")
    try:
        # Call model.run_training_pipeline, which should load the model and predict.
        # It needs to know where models are stored.
        # Assuming model.run_training_pipeline will be updated to accept model_storage_root
        # and to NOT retrain if force_retrain is False (default) and model exists.
        predictions_output = process_ticker_model_and_get_predictions(
            ticker=ticker_symbol,
            model_storage_root=MODEL_STORAGE_ROOT,
            force_retrain=False,  # Explicitly set force_retrain to False for on-demand prediction
        )

        if predictions_output:
            logger.info(
                f"Successfully retrieved {len(predictions_output.get('future_predictions_data', []))} future predictions for {ticker_symbol}."
            )
        else:
            logger.info(f"No future predictions were generated for {ticker_symbol} (model might have returned an empty list).")
        return predictions_output
    except Exception as e:
        logger.error(f"Error while getting future predictions for {ticker_symbol} (on-demand): {e}", exc_info=True)
        return {}
