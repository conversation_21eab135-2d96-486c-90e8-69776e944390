from datetime import datetime
from typing import Any, Dict, List, Tuple

import numpy as np
import pandas as pd

from .logger import get_logger

logger = get_logger()


def get_top_stocks(conn, account_id: int, limit: int = 5) -> List[Dict[str, Any]]:
    """
    Get top performing stocks for an account
    """
    cursor = conn.cursor()

    # Get current holdings and their returns
    holdings = cursor.execute(
        """
        WITH current_prices AS (
            SELECT symbol, close as current_price
            FROM cached_prices
            WHERE date = (SELECT MAX(date) FROM cached_prices)
        )
        SELECT
            t.symbol,
            SUM(t.quantity) as total_quantity,
            SUM(t.quantity * t.price) as total_cost,
            SUM(t.quantity) * cp.current_price as current_value,
            (SUM(t.quantity) * cp.current_price - SUM(t.quantity * t.price)) as absolute_return,
            (SUM(t.quantity) * cp.current_price - SUM(t.quantity * t.price)) / SUM(t.quantity * t.price) as return_rate
        FROM transactions t
        JOIN current_prices cp ON t.symbol = cp.symbol
        WHERE t.account_id = ?
        GROUP BY t.symbol
        HAVING total_quantity > 0
        ORDER BY ABS(absolute_return) DESC
        LIMIT ?
    """,
        (account_id, limit),
    ).fetchall()

    return [{"symbol": row[0], "return": row[4], "return_rate": row[5]} for row in holdings]


def calculate_holdings(account_id: int, conn) -> Dict[str, Dict[str, float]]:
    """
    Calculate current holdings and returns for an account
    """
    cursor = conn.cursor()

    holdings = cursor.execute(
        """
        WITH current_prices AS (
            SELECT symbol, close as current_price
            FROM cached_prices
            WHERE date = (SELECT MAX(date) FROM cached_prices)
        )
        SELECT
            t.symbol,
            SUM(t.quantity) as total_quantity,
            SUM(t.quantity * t.price) as total_cost,
            cp.current_price,
            SUM(t.quantity) * cp.current_price as current_value
        FROM transactions t
        JOIN current_prices cp ON t.symbol = cp.symbol
        WHERE t.account_id = ?
        GROUP BY t.symbol
        HAVING total_quantity > 0
    """,
        (account_id,),
    ).fetchall()

    return {
        row[0]: {
            "quantity": row[1],
            "cost_basis": row[2],
            "current_price": row[3],
            "current_value": row[4],
            "return": row[4] - row[2],
            "return_rate": (row[4] - row[2]) / row[2] if row[2] != 0 else 0,
        }
        for row in holdings
    }


def calculate_portfolio_value(conn, account_id=None, date=None):
    """
    计算投资组合价值，优先从portfolio_snapshots获取，如果没有则实时计算
    """
    date_param = date if date else datetime.now().strftime("%Y-%m-%d")

    # 尝试从快照中获取数据
    if account_id:
        snapshot = conn.execute(
            """
            SELECT * FROM portfolio_snapshots
            WHERE account_id = ? AND date = ?
        """,
            [account_id, date_param],
        ).fetchone()
    else:
        snapshot = conn.execute(
            """
            SELECT
                NULL as account_id,
                date,
                SUM(initial_capital) as initial_capital,
                SUM(current_value) as current_value,
                SUM(unrealized_gains) as unrealized_gains,
                SUM(realized_gains) as realized_gains,
                AVG(return_rate) as return_rate
            FROM portfolio_snapshots
            WHERE date = ?
            GROUP BY date
        """,
            [date_param],
        ).fetchone()

    if snapshot:
        return {
            snapshot["account_id"] if account_id else "all": {
                "initial_capital": snapshot["initial_capital"],
                "current_value": snapshot["current_value"],
                "unrealized_gains": snapshot["unrealized_gains"],
                "realized_gains": snapshot["realized_gains"],
                "return_rate": snapshot["return_rate"],
            }
        }

    # 如果没有快照，实时计算
    base_query = """
        WITH latest_prices AS (
            SELECT symbol, close as price, date,
                   ROW_NUMBER() OVER (PARTITION BY symbol ORDER BY date DESC) as rn
            FROM cached_prices
            WHERE date <= ?
        ),
        buy_transactions AS (
            SELECT
                t.account_id,
                SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END) as buy_amount
            FROM transactions t
            WHERE t.trans_time <= ?
            {account_filter}
            GROUP BY t.account_id
        ),
        current_holdings AS (
            SELECT
                t.account_id,
                t.symbol,
                SUM(t.quantity) as quantity,
                SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END) as buy_cost,
                SUM(CASE WHEN t.quantity < 0 THEN t.quantity * t.price ELSE 0 END) as sell_proceeds,
                COALESCE(lp.price, MAX(t.price)) as current_price
            FROM transactions t
            LEFT JOIN (SELECT * FROM latest_prices WHERE rn = 1) lp
                ON t.symbol = lp.symbol
            WHERE t.trans_time <= ?
            {account_filter}
            GROUP BY t.account_id, t.symbol
            HAVING quantity != 0
        )
        SELECT
            ch.account_id,
            bt.buy_amount as initial_capital,
            SUM(ch.quantity * ch.current_price) as current_value,
            SUM(ch.quantity * ch.current_price - ch.buy_cost + ABS(ch.sell_proceeds)) as total_gains,
            (SELECT COALESCE(SUM(realized_gain), 0)
             FROM realized_gains rg
             WHERE rg.account_id = ch.account_id
             AND rg.sell_date <= ?) as realized_gains
        FROM current_holdings ch
        JOIN buy_transactions bt ON ch.account_id = bt.account_id
        GROUP BY ch.account_id
    """

    if account_id:
        account_filter = "AND t.account_id = ?"
        params = [date_param, date_param, date_param, date_param, account_id, account_id]
    else:
        account_filter = ""
        params = [date_param, date_param, date_param, date_param]

    query = base_query.format(account_filter=account_filter)
    results = conn.execute(query, params).fetchall()

    portfolio_values = {}
    for row in results:
        account_id = row["account_id"]
        initial_capital = row["initial_capital"] or 0
        current_value = row["current_value"] or 0
        total_gains = row["total_gains"] or 0
        realized_gains = row["realized_gains"] or 0
        unrealized_gains = total_gains - realized_gains

        return_rate = (total_gains / initial_capital) if initial_capital > 0 else 0

        portfolio_values[account_id] = {
            "initial_capital": initial_capital,
            "current_value": current_value,
            "unrealized_gains": unrealized_gains,
            "realized_gains": realized_gains,
            "return_rate": return_rate,
        }

        # 保存快照
        conn.execute(
            """
            INSERT OR REPLACE INTO portfolio_snapshots (
                account_id, date, initial_capital, current_value,
                unrealized_gains, realized_gains, return_rate
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
        """,
            [account_id, date_param, initial_capital, current_value, unrealized_gains, realized_gains, return_rate],
        )

    conn.commit()
    return portfolio_values


def calculate_realized_gains(conn, account_id, transaction):
    """
    计算并记录已实现收益
    使用FIFO方法计算已实现收益，并处理股票拆分/反向拆分
    """
    symbol = transaction["symbol"]
    sell_quantity = abs(transaction["quantity"])
    sell_price = transaction["price"]
    sell_date = transaction["trans_time"]

    logger.info(
        f"Calculating realized gains for Acc {account_id}, Sell {symbol}, Qty: {sell_quantity}, Price: {sell_price}, Date: {sell_date}"
    )

    # 获取此卖出交易之前的所有买入交易批次
    buy_transactions_cursor = conn.execute(
        """
        SELECT transaction_id, trans_time, quantity, price
        FROM transactions
        WHERE account_id = ? AND symbol = ? AND quantity > 0
        AND date(trans_time) <= date(?)
        ORDER BY trans_time ASC
    """,
        [account_id, symbol, sell_date],
    )
    buy_lots_raw = buy_transactions_cursor.fetchall()

    if not buy_lots_raw:
        return 0  # Cannot calculate gain without buy lots

    # --- Split Adjustment Logic ---
    # Find potential split events between the earliest buy and the sell date
    earliest_buy_date = min(row["trans_time"] for row in buy_lots_raw)
    split_transactions_cursor = conn.execute(
        """
        SELECT trans_time, quantity, price
        FROM transactions
        WHERE account_id = ? AND symbol = ?
        AND date(trans_time) > date(?)
        AND date(trans_time) < date(?)
        ORDER BY trans_time ASC, quantity ASC
    """,
        [account_id, symbol, earliest_buy_date, sell_date],
    )
    split_candidates = split_transactions_cursor.fetchall()

    split_factors = {}
    # Very basic split detection: look for adjacent FROM/TO on the same day
    i = 0
    while i < len(split_candidates) - 1:
        from_trans = split_candidates[i]
        to_trans = split_candidates[i + 1]

        # Check if on the same day, one negative quantity, one positive
        if (
            pd.to_datetime(from_trans["trans_time"]).date() == pd.to_datetime(to_trans["trans_time"]).date()
            and from_trans["quantity"] < 0
            and to_trans["quantity"] > 0
        ):

            split_date = pd.to_datetime(from_trans["trans_time"]).date()
            from_qty = abs(from_trans["quantity"])
            to_qty = to_trans["quantity"]

            if from_qty > 0 and to_qty > 0:
                # Calculate ratio (new shares / old shares)
                ratio = to_qty / from_qty
                split_factors[split_date] = ratio
                i += 2  # Skip both processed transactions
            else:
                i += 1
        else:
            i += 1
    # --- End Split Adjustment Logic ---

    buy_lots_adjusted = []
    for lot in buy_lots_raw:
        adjusted_lot = dict(lot)  # Create a mutable copy
        buy_date = pd.to_datetime(adjusted_lot["trans_time"]).date()

        # Apply cumulative split factors for splits that occurred *after* this buy lot
        cumulative_factor = 1.0
        for split_date, ratio in sorted(split_factors.items()):
            if buy_date < split_date:
                cumulative_factor *= ratio

        if cumulative_factor != 1.0:
            original_qty = adjusted_lot["quantity"]
            original_price = adjusted_lot["price"]
            adjusted_lot["quantity"] = original_qty * cumulative_factor
            adjusted_lot["price"] = original_price / cumulative_factor  # Adjust price inversely

        buy_lots_adjusted.append(adjusted_lot)

    # --- FIFO Matching with Adjusted Lots ---
    remaining_sell_quantity = sell_quantity
    total_realized_gain = 0
    cost_basis_details = []  # To store details for logging/debugging
    lots_consumed = []  # Track which buy lots are fully/partially used

    # Delete existing realized_gains entries for this specific sell transaction first
    # This prevents duplicates if the calculation is re-run
    try:
        del_cursor = conn.execute(
            """
            DELETE FROM realized_gains
            WHERE account_id = ? AND symbol = ? AND sell_date = ? AND sell_price = ?
        """,
            [account_id, symbol, sell_date, sell_price],
        )
        logger.info(f"Deleted {del_cursor.rowcount} existing realized_gains entries for this sell transaction.")
    except Exception as e_del:
        logger.error(f"Error deleting existing realized_gains: {e_del}")
        # Decide whether to proceed or raise based on policy
        # raise # Or just log and continue

    for buy_lot in buy_lots_adjusted:
        if remaining_sell_quantity <= 1e-9:  # Use tolerance for float comparison
            break

        # Use adjusted values
        buy_lot_qty_available = buy_lot["quantity"]  # Current available quantity in this adjusted lot
        buy_lot_price_adjusted = buy_lot["price"]
        buy_lot_date = buy_lot["trans_time"]

        # How much can we match from this buy lot?
        matched_quantity = min(remaining_sell_quantity, buy_lot_qty_available)

        if matched_quantity <= 1e-9:
            continue  # Skip if negligible quantity to match

        # Calculate gain for this matched portion
        gain_for_portion = matched_quantity * (sell_price - buy_lot_price_adjusted)
        total_realized_gain += gain_for_portion

        # Record the details for this specific buy-sell match
        try:
            conn.execute(
                """
                INSERT INTO realized_gains (
                    account_id, symbol, buy_date, sell_date,
                    buy_quantity, buy_price, sell_price, realized_gain
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """,
                [
                    account_id,
                    symbol,
                    buy_lot_date,
                    sell_date,
                    matched_quantity,
                    buy_lot_price_adjusted,  # Use adjusted buy price
                    sell_price,
                    gain_for_portion,
                ],
            )
            cost_basis_details.append(
                f"Matched {matched_quantity:.4f} shares bought on {buy_lot_date} at adjusted price {buy_lot_price_adjusted:.4f}"
            )
        except Exception as e_ins:
            logger.error(f"Error inserting realized_gains record: {e_ins}")
            # Rollback might be needed here or handled higher up

        remaining_sell_quantity -= matched_quantity

        # Note: We don't need to update the buy_lot quantity in the DB,
        # as we fetch fresh each time. This logic assumes the function
        # is called once per sell transaction.

    return total_realized_gain


def calculate_current_market_value(conn, symbol: str = None, account_id: int = None) -> Tuple[float, float, float]:
    """
    计算当前市值、成本和收益率

    Args:
        conn: 数据库连接
        symbol: 可选，指定股票代码
        account_id: 可选，指定账户ID

    Returns:
        Tuple[float, float, float]: (当前市值, 成本, 收益率)
    """
    try:
        query = """
            WITH current_prices AS (
                SELECT symbol, close as current_price
                FROM cached_prices
                WHERE date = (SELECT MAX(date) FROM cached_prices)
            ),
            all_transactions AS (
                SELECT
                    t.symbol,
                    SUM(t.quantity) as total_quantity,
                    SUM(CASE WHEN t.quantity > 0 THEN t.quantity * t.price ELSE 0 END) as buy_cost,
                    SUM(CASE WHEN t.quantity < 0 THEN ABS(t.quantity) * t.price ELSE 0 END) as sell_proceeds,
                    SUM(CASE WHEN t.quantity > 0 THEN t.quantity ELSE 0 END) as total_bought,
                    SUM(CASE WHEN t.quantity < 0 THEN ABS(t.quantity) ELSE 0 END) as total_sold
                FROM transactions t
                WHERE t.symbol != 'CASH'  -- 排除CASH交易
                {conditions}
                GROUP BY t.symbol
                HAVING total_quantity > 0
            )
            SELECT
                t.symbol,
                t.total_quantity,
                t.buy_cost,
                t.sell_proceeds,
                t.total_bought,
                t.total_sold,
                cp.current_price,
                t.total_quantity * cp.current_price as position_value,
                CASE
                    WHEN t.total_bought > 0 THEN
                        (t.buy_cost - COALESCE(t.sell_proceeds, 0)) * (t.total_quantity / t.total_bought)
                    ELSE 0
                END as adjusted_cost
            FROM all_transactions t
            JOIN current_prices cp ON t.symbol = cp.symbol
        """

        conditions = []
        params = []

        if symbol:
            conditions.append("AND t.symbol = ?")
            params.append(symbol)
        if account_id:
            conditions.append("AND t.account_id = ?")
            params.append(account_id)

        query = query.format(conditions=" ".join(conditions))

        results = conn.execute(query, params).fetchall()

        if results:
            total_cost = sum(row["adjusted_cost"] for row in results)
            current_value = sum(row["position_value"] for row in results)
            return_rate = ((current_value - total_cost) / total_cost) if total_cost > 0 else 0
            return current_value, total_cost, return_rate

        return 0.0, 0.0, 0.0

    except Exception as e:
        print(f"Error calculating market value: {str(e)}")
        return 0.0, 0.0, 0.0


def calculate_return_rate(current_value: float, total_cost: float) -> float:
    """
    计算收益率

    Args:
        current_value: 当前市值
        total_cost: 总成本

    Returns:
        float: 收益率
    """
    return ((current_value - total_cost) / total_cost) if total_cost > 0 else 0.0


def calculate_twr(conn, account_id=None):
    """
    Calculate the Time-Weighted Return (TWR) for a given account or all accounts.

    Args:
        conn: Database connection object.
        account_id (int, optional): The account ID to calculate TWR for.
                                    If None, calculates for all accounts combined.

    Returns:
        float: The annualized Time-Weighted Return, or 0.0 if calculation is not possible.
    """
    logger.info(f"Starting TWR calculation for account_id: {account_id}")

    try:
        # --- 1. Fetch Data ---
        account_filter = f"WHERE account_id = {account_id}" if account_id else ""

        transactions_query = f"""
            SELECT trans_time, symbol, quantity, price, account_id
            FROM transactions
            {account_filter}
            ORDER BY trans_time
        """
        transactions = pd.read_sql(transactions_query, conn, parse_dates=["trans_time"])

        if transactions.empty:
            logger.warning("No transactions found for TWR calculation.")
            return 0.0

        # Get unique symbols and date range
        symbols = transactions["symbol"].unique().tolist()
        # Ensure start_date is a pandas Timestamp before normalizing
        min_trans_time = transactions["trans_time"].min()
        start_date = (
            min_trans_time.normalize() if pd.notna(min_trans_time) else pd.Timestamp.now().normalize()
        )  # Fallback if min is NaT
        end_date = pd.Timestamp.now().normalize()  # Use pandas Timestamp for normalize()

        # Ensure 'CASH' is handled correctly if present
        if "CASH" in symbols:
            symbols.remove("CASH")

        prices_query = f"""
            SELECT date, symbol, close
            FROM cached_prices
            WHERE symbol IN ({','.join(['?'] * len(symbols))})
            AND date >= ? AND date <= ?
        """
        params = symbols + [start_date.strftime("%Y-%m-%d"), end_date.strftime("%Y-%m-%d")]
        prices = pd.read_sql(prices_query, conn, params=params, parse_dates=["date"], index_col="date")

        if prices.empty and symbols:  # If only CASH transactions exist
            logger.warning("No price data found for symbols, but transactions exist.")
            # Potentially handle this case - maybe return based on cash changes only?
            # For now, returning 0 as we can't value holdings.
            return 0.0
        elif prices.empty and not symbols:  # Only CASH transactions exist
            logger.info("Only CASH transactions found. TWR calculation based on cash.")
            # Proceed, but portfolio value will only be cash value
            pass

        # Pivot prices for easy lookup: Date x Symbol -> Price
        if not prices.empty:
            prices_pivot = prices.pivot(columns="symbol", values="close")
            # Fill missing prices with the last known price (forward fill)
            prices_pivot = prices_pivot.ffill()
        else:
            prices_pivot = pd.DataFrame(index=pd.date_range(start_date, end_date))  # Empty if no stock prices

        # --- 2. Prepare Daily Data ---
        # Create a date range for the entire period
        all_dates = pd.date_range(start=start_date, end=end_date, freq="D")

        # Reindex prices to include all dates, forward-filling missing market data
        prices_pivot = prices_pivot.reindex(all_dates).ffill()

        # Calculate daily cash flows and holdings
        daily_summary = pd.DataFrame(index=all_dates)
        daily_summary["cash_flow"] = 0.0

        # Group transactions by day
        transactions["date"] = transactions["trans_time"].dt.normalize()
        daily_transactions = transactions.groupby("date")

        # Initialize holdings DataFrame (Symbol -> Quantity)
        holdings = pd.Series(dtype=float)

        # Process initial state before the first transaction date
        first_trans_date = transactions["date"].min()

        # --- 3. Iterate Through Time to Calculate Daily Values ---
        portfolio_values = pd.Series(index=all_dates, dtype=float)
        cash_balance = 0.0

        # Initialize values for the day before the first transaction
        prev_date = first_trans_date - pd.Timedelta(days=1)
        portfolio_values[prev_date] = 0.0  # Start with zero value

        for date in all_dates:

            # --- a. Calculate Value at Start of Day (End of Previous Day) ---
            market_value_start = 0.0
            stock_holdings = holdings[holdings.index != "CASH"]  # Filter out CASH
            if not stock_holdings.empty and date in prices_pivot.index:
                # Ensure we only try to access prices for symbols actually in prices_pivot columns
                valid_symbols = stock_holdings.index.intersection(prices_pivot.columns)
                if not valid_symbols.empty:
                    current_prices = prices_pivot.loc[date, valid_symbols].fillna(0)
                    market_value_start = (stock_holdings[valid_symbols] * current_prices).sum()

            portfolio_value_start = market_value_start + cash_balance  # Add cash balance separately

            # --- b. Process Transactions for the Current Day ---
            cash_flow_today = 0.0
            if date in daily_transactions.groups:
                day_trans = daily_transactions.get_group(date)
                for _, trans in day_trans.iterrows():
                    symbol = trans["symbol"]
                    quantity = trans["quantity"]
                    price = trans["price"]

                    # Update holdings
                    holdings[symbol] = holdings.get(symbol, 0) + quantity

                    # Update cash balance & track cash flow
                    transaction_value = quantity * price
                    cash_balance -= transaction_value  # Decrease cash for buy, increase for sell/deposit

                    # TWR Cash Flow:
                    # - Positive for deposits (e.g., initial 'CASH' transaction)
                    # - Negative for withdrawals
                    # - Buys/Sells of stocks are NOT external cash flows for TWR
                    if symbol == "CASH":
                        cash_flow_today += quantity  # Deposits are positive quantity
                        # Treat initial cash as deposit
                        # We already adjusted cash_balance above for CASH transactions

            # Remove symbols with zero quantity
            holdings = holdings[holdings != 0]
            daily_summary.loc[date, "cash_flow"] = cash_flow_today

            # --- c. Calculate Value at End of Day ---
            market_value_end = 0.0
            stock_holdings_end = holdings[holdings.index != "CASH"]  # Filter out CASH again after updates
            if not stock_holdings_end.empty and date in prices_pivot.index:
                # Ensure we only try to access prices for symbols actually in prices_pivot columns
                valid_symbols_end = stock_holdings_end.index.intersection(prices_pivot.columns)
                if not valid_symbols_end.empty:
                    current_prices_end = prices_pivot.loc[date, valid_symbols_end].fillna(0)
                    market_value_end = (stock_holdings_end[valid_symbols_end] * current_prices_end).sum()

            portfolio_value_end = market_value_end + cash_balance  # Add cash balance separately
            portfolio_values[date] = portfolio_value_end

        # --- 4. Calculate Daily Returns (Holding Period Returns) ---
        hpr = pd.Series(index=all_dates, dtype=float)
        for date in all_dates:
            prev_date = date - pd.Timedelta(days=1)
            if prev_date in portfolio_values.index:
                val_end = portfolio_values[date]
                val_start = portfolio_values[prev_date]
                cash_flow = daily_summary.loc[date, "cash_flow"]

                # HPR = (Ending Value - Starting Value - Cash Flow) / Starting Value
                # Or simplified: HPR = (Ending Value / (Starting Value + Cash Flow)) - 1
                denominator = val_start + cash_flow
                if denominator != 0 and not pd.isna(denominator) and not pd.isna(val_end):
                    hpr[date] = (val_end / denominator) - 1
                elif val_start == 0 and cash_flow == 0 and val_end == 0:
                    hpr[date] = 0.0  # No change from zero
                else:
                    hpr[date] = 0.0  # Default to 0 if calculation is invalid (e.g., division by zero)
                    logger.warning(
                        f"Could not calculate HPR for {date}. Values: End={val_end}, Start={val_start}, CF={cash_flow}"
                    )

        # Drop initial NaN value if calculation started day before first transaction
        hpr = hpr.dropna()
        if hpr.empty:
            logger.warning("Holding Period Returns series is empty. Cannot calculate TWR.")
            return 0.0

        # --- 5. Link HPRs and Annualize ---
        # Geometric linking: (1 + HPR1) * (1 + HPR2) * ... * (1 + HPRn) - 1
        total_return = (hpr + 1).prod() - 1

        # Annualize
        num_days = (hpr.index.max() - hpr.index.min()).days + 1
        if num_days <= 0:
            logger.warning("Number of days for annualization is zero or negative.")
            return total_return  # Return total return if period too short

        years = num_days / 365.25

        # Check if the base for exponentiation is positive
        base = 1 + total_return
        if base <= 0:
            # Cannot annualize a loss >= 100% with a fractional exponent
            # Cap annualized return at -100%
            annualized_twr = -1.0
            logger.warning(f"Total return ({total_return:.4f}) is <= -1. Capping annualized TWR at -100%.")
        else:
            annualized_twr = base ** (1 / years) - 1

        logger.info(f"TWR Calculation Complete. Annualized TWR: {annualized_twr:.4f}")
        return annualized_twr

    except Exception as e:
        logger.error(f"Error calculating TWR: {e}", exc_info=True)
        return 0.0  # Return 0 in case of error
