import json
import os
import os.path
import re
import traceback

import requests
from openai import OpenAI

from .logger import get_logger

# 直接从文件读取配置
logger = get_logger()


def read_env_file():
    """直接从.env文件读取配置，不依赖环境变量"""
    config = {
        "LLM_API_ENDPOINT": "https://api.deepseek.com",  # 默认值
        "LLM_API_KEY": "",
        "LLM_MODEL": "deepseek-chat",  # 默认值
    }

    # 可能的.env文件位置
    possible_paths = [
        os.path.abspath(".env"),  # 当前工作目录
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "..", ".env"),  # utils目录
        os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "..", ".env"),  # backend目录
        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "..", ".env"),  # 项目根目录
    ]

    env_file = None
    # 查找.env文件
    for path in possible_paths:
        if os.path.exists(path):
            env_file = path
            break

    if not env_file:
        logger.warning(f"未找到.env文件，使用默认配置。尝试过以下路径: {possible_paths}")
        return config

    # 读取.env文件内容
    try:
        with open(env_file, "r") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    key, value = line.split("=", 1)
                    key = key.strip()
                    value = value.strip().strip('"').strip("'")
                    if key in config:
                        config[key] = value
    except Exception as e:
        logger.error(f"读取.env文件出错: {str(e)}")

    return config


# 加载配置
config = read_env_file()
LLM_API_ENDPOINT = config["LLM_API_ENDPOINT"]
LLM_API_KEY = config["LLM_API_KEY"]
LLM_MODEL = config["LLM_MODEL"]

logger.info(f"最终配置: LLM_API_ENDPOINT={LLM_API_ENDPOINT}")
logger.info(f"最终配置: LLM_MODEL={LLM_MODEL}")
logger.info(f"最终配置: LLM_API_KEY={'已设置' if LLM_API_KEY else '未设置'}")


# 修复 API 端点 URL 格式问题
def normalize_api_endpoint(url):
    """确保 API 端点不包含路径部分如 /v1/chat/completions"""
    if not url:
        return url
    # 如果 URL 包含 /v1/chat/completions 路径，删除这部分
    if "/v1/chat/completions" in url:
        url = url.split("/v1/chat/completions")[0]
    # 确保 URL 不以斜杠结尾
    if url.endswith("/"):
        url = url[:-1]
    return url


# LLM服务类
class LLMService:
    def __init__(self):
        normalized_endpoint = normalize_api_endpoint(LLM_API_ENDPOINT)
        self.client = OpenAI(api_key=LLM_API_KEY, base_url=normalized_endpoint)

    @staticmethod
    def ensure_json_format(text):
        """确保文本是有效的JSON格式，或尝试修复它"""
        # 尝试直接解析
        try:
            return json.loads(text)
        except json.JSONDecodeError:
            logger.warning("收到的响应不是有效的JSON格式，尝试修复")

            # 尝试提取JSON部分
            try:
                # 寻找JSON的开始和结束
                start_pos = text.find("{")
                end_pos = text.rfind("}") + 1

                if start_pos >= 0 and end_pos > start_pos:
                    json_str = text[start_pos:end_pos]
                    # 尝试清理和修复常见的JSON格式问题
                    json_str = re.sub(r",\s*}", "}", json_str)  # 移除末尾逗号
                    json_str = re.sub(r",\s*]", "]", json_str)  # 移除数组末尾逗号

                    # 尝试解析修复后的JSON
                    return json.loads(json_str)
            except Exception as e:
                logger.error(f"修复JSON失败: {str(e)}")

            # 如果所有尝试都失败，返回空对象
            logger.error("无法将响应转换为有效的JSON，返回空对象")
            return {}

    def call_llm_api_json(self, prompt, temperature=0.7, max_tokens=1500):
        """调用LLM API并确保返回有效的JSON格式"""
        text_response = self.call_llm_api(prompt, temperature, max_tokens)
        return self.ensure_json_format(text_response)

    @staticmethod
    def call_llm_api(prompt, temperature=0.7, max_tokens=1500):
        """调用LLM API获取响应"""
        try:
            logger.info(f"开始调用LLM API - 模型: {LLM_MODEL}, 请求长度: {len(prompt)}")
            if not LLM_API_KEY:
                logger.error("LLM_API_KEY未配置")
                return "API密钥未配置，无法调用LLM服务"

            if not LLM_API_ENDPOINT:
                logger.error("LLM_API_ENDPOINT未配置")
                return "API端点未配置，无法调用LLM服务"

            messages = [
                {
                    "role": "system",
                    "content": "你是一名专业的美国股市投资分析师，擅长分析股票和投资组合，根据最新的各种消息和数据，提供客观、有见地的建议。严格按照指定的JSON格式输出，确保输出的内容是有效的JSON。不要添加反引号或```json这样的标记，直接返回纯JSON对象。",
                },
                {"role": "user", "content": prompt},
            ]

            normalized_endpoint = normalize_api_endpoint(LLM_API_ENDPOINT)
            client = OpenAI(api_key=LLM_API_KEY, base_url=normalized_endpoint)

            response = client.chat.completions.create(model=LLM_MODEL, messages=messages, stream=False)

            logger.info("LLM API响应已收到")

            if response.choices and response.choices[0].message.content:
                content = response.choices[0].message.content
                logger.info(f"LLM API调用成功，返回内容长度: {len(content)}")
                logger.debug(f"LLM返回内容预览: {content[:100]}...")
                return content
            else:
                logger.error("LLM API调用失败: 响应无内容")
                return "API调用失败: 响应无内容"

        except requests.exceptions.Timeout:
            logger.error("LLM API调用超时")
            return "API调用超时，请稍后再试"
        except requests.exceptions.ConnectionError:
            logger.error("LLM API连接错误")
            return "无法连接到API服务，请检查网络连接"
        except Exception as e:
            logger.error(f"调用LLM API时出错: {str(e)}")
            logger.error(traceback.format_exc())
            return f"发生错误: {str(e)}"
