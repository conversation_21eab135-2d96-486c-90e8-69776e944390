import numpy as np
import pandas as pd
import pandas_ta as ta


def calculate_wilder_rsi(series: pd.Series, period: int = 14) -> pd.Series:
    """
    Calculates Relative Strength Index (RSI) using <PERSON>'s smoothing method.

    Args:
        series (pd.Series): Series of prices (e.g., 'Close' prices).
        period (int): The lookback period for RSI calculation. Default is 14.

    Returns:
        pd.Series: A series containing the RSI values.
    """
    if series.empty or len(series.dropna()) <= period:
        # Return an empty series with the same index if not enough data or series is empty
        return pd.Series(index=series.index, dtype="float64")

    delta = series.diff()

    # Ensure gains and losses are positive floats
    gain = delta.where(delta > 0, 0.0).astype("float64")
    loss = (-delta.where(delta < 0, 0.0)).astype("float64")  # ensure loss is positive

    # Pre-allocate series for average gain and loss
    avg_gain = pd.Series(index=series.index, dtype="float64")
    avg_loss = pd.Series(index=series.index, dtype="float64")

    # Calculate initial average for the first 'period'
    # delta.iloc[0] is NaN, so first actual change is delta.iloc[1]
    # First 'period' actual gains are gain.iloc[1] through gain.iloc[period]
    # Their mean corresponds to the RSI value at series.index[period]
    avg_gain.iloc[period] = gain.iloc[1 : period + 1].mean()
    avg_loss.iloc[period] = loss.iloc[1 : period + 1].mean()

    # Smooth for subsequent periods
    for i in range(period + 1, len(series)):
        avg_gain.iloc[i] = (avg_gain.iloc[i - 1] * (period - 1) + gain.iloc[i]) / period
        avg_loss.iloc[i] = (avg_loss.iloc[i - 1] * (period - 1) + loss.iloc[i]) / period

    # Calculate RS
    # Handle division by zero: if avg_loss is 0, rs is np.inf or np.nan
    rs = avg_gain / avg_loss

    # Calculate RSI
    rsi_series_raw = 100.0 - (100.0 / (1.0 + rs))

    # RSI should be 100 if avg_loss is 0 (rs is inf)
    rsi_series_raw.loc[rs == np.inf] = 100.0
    # RSI can be NaN if both avg_gain and avg_loss are 0 initially, or avg_loss is 0 and avg_gain is also 0.
    # It's common to fill these as 50 (neutral) or leave as NaN depending on preference.
    # For this utility, we return the raw calculation including potential NaNs at the start.

    return rsi_series_raw


def calculate_macd(series: pd.Series, short_window: int = 12, long_window: int = 26, signal_window: int = 9):
    """
    Calculates Moving Average Convergence Divergence (MACD) and identifies Golden/Death Crosses.

    Args:
        series (pd.Series): Series of prices (e.g., 'Close' prices).
        short_window (int): The lookback period for the short-term EMA.
        long_window (int): The lookback period for the long-term EMA.
        signal_window (int): The lookback period for the signal line EMA.

    Returns:
        tuple:
            - pd.DataFrame: A DataFrame with 'MACD', 'Signal_Line', and 'MACD_Histogram' columns.
            - list: A list of indices where a Golden Cross occurred.
            - list: A list of indices where a Death Cross occurred.
            - list: A list of indices where a Second Golden Cross occurred (within 20 days of first).
    """
    if series.empty or len(series.dropna()) < long_window:
        # Return empty structures if not enough data
        empty_df = pd.DataFrame(index=series.index, columns=["MACD", "Signal_Line", "MACD_Histogram"], dtype="float64")
        return empty_df, [], [], []

    short_ema = series.ewm(span=short_window, adjust=False).mean()
    long_ema = series.ewm(span=long_window, adjust=False).mean()

    macd_line = short_ema - long_ema
    signal_line = macd_line.ewm(span=signal_window, adjust=False).mean()
    macd_histogram = macd_line - signal_line

    df = pd.DataFrame({"MACD": macd_line, "Signal_Line": signal_line, "MACD_Histogram": macd_histogram}, index=series.index)

    # --- Find Golden and Death Crosses ---
    # A Golden Cross occurs when the MACD line crosses above the Signal line.
    # A Death Cross occurs when the MACD line crosses below the Signal line.

    # Identify points where the MACD line is above the signal line
    above = macd_line > signal_line
    # Identify points where the MACD line was below the signal line in the previous period
    was_below = macd_line.shift(1) < signal_line.shift(1)

    # A golden cross is where it's now above and was previously below
    golden_crosses = df.index[above & was_below].tolist()

    # A death cross is the opposite
    below = macd_line < signal_line
    was_above = macd_line.shift(1) > signal_line.shift(1)
    death_crosses = df.index[below & was_above].tolist()

    # --- Find Second Golden Crosses ---
    # A second golden cross occurs within 20 trading days of a previous golden cross
    second_golden_crosses = []
    second_golden_window = 20  # 20个交易日窗口

    for i, current_cross in enumerate(golden_crosses):
        # 检查之前的金叉
        for j in range(i):
            previous_cross = golden_crosses[j]
            # 计算两个金叉之间的交易日差距
            try:
                # 如果索引是DatetimeIndex，计算日期差
                if isinstance(df.index, pd.DatetimeIndex):
                    days_diff = (current_cross - previous_cross).days
                else:
                    # 如果是其他类型的索引，使用位置差
                    current_pos = df.index.get_loc(current_cross)
                    previous_pos = df.index.get_loc(previous_cross)
                    days_diff = current_pos - previous_pos

                # 如果在指定窗口内，且中间有死叉（确保是真正的二次金叉）
                if 1 <= days_diff <= second_golden_window:
                    # 检查两次金叉之间是否有死叉
                    crosses_between = [dc for dc in death_crosses if previous_cross < dc < current_cross]
                    if crosses_between:  # 有死叉在中间，这是真正的二次金叉
                        second_golden_crosses.append(current_cross)
                        break  # 找到一个就够了，不需要继续检查更早的金叉
            except (KeyError, ValueError):
                continue

    return df, golden_crosses, death_crosses, second_golden_crosses


def calculate_sma(data_series, window):
    """
    Calculates Simple Moving Average (SMA).
    Args:
        data_series (pd.Series): Series of data (e.g., closing prices).
        window (int): SMA window period.
    Returns:
        pd.Series: SMA values.
    """
    return data_series.rolling(window=window).mean()


def calculate_bollinger_bands(data_series, window=20, num_std_dev=2):
    """
    Calculates Bollinger Bands.
    Args:
        data_series (pd.Series): Series of data (e.g., closing prices).
        window (int): Moving average window period.
        num_std_dev (int): Number of standard deviations for the bands.
    Returns:
        tuple: (pd.Series, pd.Series, pd.Series) for Middle, Upper, Lower bands.
    """
    middle_band = data_series.rolling(window=window).mean()
    std_dev = data_series.rolling(window=window).std()
    upper_band = middle_band + (std_dev * num_std_dev)
    lower_band = middle_band - (std_dev * num_std_dev)
    return middle_band, upper_band, lower_band


def calculate_atr(high_prices, low_prices, close_prices, window=14):
    """
    Calculates Average True Range (ATR).
    Requires pd.Series for high, low, and close prices.
    """
    # Ensure inputs are pandas Series
    if not (isinstance(high_prices, pd.Series) and isinstance(low_prices, pd.Series) and isinstance(close_prices, pd.Series)):
        # Attempt to return a Series of NaNs with the same index as one of the inputs if possible
        index_ref = None
        if isinstance(high_prices, pd.Series):
            index_ref = high_prices.index
        elif isinstance(low_prices, pd.Series):
            index_ref = low_prices.index
        elif isinstance(close_prices, pd.Series):
            index_ref = close_prices.index
        return pd.Series(index=index_ref, dtype=np.float64)

    high_low = high_prices - low_prices
    high_close_prev = np.abs(high_prices - close_prices.shift(1))
    low_close_prev = np.abs(low_prices - close_prices.shift(1))

    # Create a DataFrame for TR components and find the max for each row
    # Fill NaN for the first row of prev_close dependent calculations
    tr_components = pd.DataFrame({"hl": high_low, "hc_prev": high_close_prev, "lc_prev": low_close_prev})
    tr = tr_components.max(axis=1)

    # Handle the first TR value which could be NaN if using shift(1) for prev_close
    # A common practice is to use high - low for the first TR value
    if pd.isna(tr.iloc[0]) and len(tr) > 0:
        if not pd.isna(high_low.iloc[0]):
            tr.iloc[0] = high_low.iloc[0]
        else:  # if high_low itself is NaN for the first element (e.g. if high/low are NaN)
            tr.iloc[0] = 0  # Or handle as appropriate, e.g. propagate NaN

    atr = tr.rolling(window=window, min_periods=1).mean()  # Use min_periods=1 for rolling mean
    return atr


def calculate_historical_volatility(data_series, window=20):
    """
    Calculates Historical Volatility.
    Args:
        data_series (pd.Series): Series of data (e.g., closing prices).
        window (int): Volatility window period.
    Returns:
        pd.Series: Historical Volatility values.
    """
    log_returns = np.log(data_series / data_series.shift(1))
    # Calculate rolling standard deviation of log returns, annualized
    historical_volatility = log_returns.rolling(window=window, min_periods=window).std() * np.sqrt(252)
    return historical_volatility


def calculate_roc(data_series, window=12):
    """
    Calculates Rate of Change (ROC).
    Args:
        data_series (pd.Series): Series of data (e.g., closing prices).
        window (int): ROC window period.
    Returns:
        pd.Series: ROC values.
    """
    roc = ((data_series - data_series.shift(window)) / data_series.shift(window)) * 100
    return roc


def calculate_adx(high: pd.Series, low: pd.Series, close: pd.Series, window: int = 14) -> pd.Series:
    """Calculate the Average Directional Index (ADX) using pandas_ta"""
    adx_df = ta.adx(high, low, close, length=window)
    return (
        adx_df.iloc[:, 0]
        if isinstance(adx_df, pd.DataFrame) and not adx_df.empty
        else pd.Series(index=close.index, dtype=np.float64, name="ADX_14")
    )


def calculate_supersmoother(series: pd.Series, period: int) -> pd.Series:
    """
    Calculates the Ehlers SuperSmoother filter in a robust way.
    """
    arg = np.sqrt(2) * np.pi / period
    a1 = np.exp(-arg)
    b1 = 2 * a1 * np.cos(arg)
    c2 = b1
    c3 = -a1 * a1
    c1 = 1 - c2 - c3

    smoothed = pd.Series(np.nan, index=series.index)
    series_ffill = series.copy().ffill()

    for i in range(len(series)):
        if i >= 2:
            s_i_minus_2 = (
                smoothed.iloc[i - 2]
                if pd.notna(smoothed.iloc[i - 2])
                else (series_ffill.iloc[i - 2] if pd.notna(series_ffill.iloc[i - 2]) else 0)
            )
            s_i_minus_1 = (
                smoothed.iloc[i - 1]
                if pd.notna(smoothed.iloc[i - 1])
                else (series_ffill.iloc[i - 1] if pd.notna(series_ffill.iloc[i - 1]) else 0)
            )

            p_i = series_ffill.iloc[i] if pd.notna(series_ffill.iloc[i]) else s_i_minus_1
            p_i_minus_1 = series_ffill.iloc[i - 1] if pd.notna(series_ffill.iloc[i - 1]) else p_i

            price_term = c1 * (p_i + p_i_minus_1) / 2
            smoothed.iloc[i] = price_term + c2 * s_i_minus_1 + c3 * s_i_minus_2
        elif i == 1:
            smoothed.iloc[i] = (
                series_ffill.iloc[i]
                if pd.notna(series_ffill.iloc[i])
                else (series_ffill.iloc[0] if pd.notna(series_ffill.iloc[0]) else 0)
            )
        elif i == 0:
            smoothed.iloc[i] = series_ffill.iloc[i] if pd.notna(series_ffill.iloc[i]) else 0

    return smoothed.reindex(series.index)
