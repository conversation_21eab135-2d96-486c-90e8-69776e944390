import atexit
import functools
import os
import sys

from loguru import logger

# Add at the top of the file
_IS_CONFIGURED = False
_HANDLER_IDS = []  # Track handler IDs for proper cleanup
log_path = os.path.join(os.path.dirname(__file__), "..", "..", "logs")


def cleanup_logger():
    """Clean up logger handlers during shutdown"""
    global _HANDLER_IDS
    try:
        for handler_id in _HANDLER_IDS:
            logger.remove(handler_id)
        _HANDLER_IDS = []
    except Exception:
        pass


def configure_logger():
    """Configure logger with rotation, retention and environment-based settings"""
    global _IS_CONFIGURED, _HANDLER_IDS
    os.makedirs(log_path, exist_ok=True)

    logger.remove()  # Remove default handler
    _HANDLER_IDS = []  # Reset handler IDs

    # Add file handler with rotation and compression
    file_handler_id = logger.add(
        os.path.join(log_path, "app_{time:YYYY-MM-DD}.log"),
        rotation="00:00",  # Daily rotation
        retention="30 days",
        compression="zip",
        enqueue=True,
        format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level: <8} | {module}:{function}:{line} - {message}",
    )
    _HANDLER_IDS.append(file_handler_id)

    # Add console handler
    console_handler_id = logger.add(
        sink=sys.stderr,
        level="DEBUG" if os.getenv("LOG_ENV") == "DEVELOPMENT" else "INFO",
        format="<green>{time:HH:mm:ss.SSS}</green> | {level.icon} | <cyan>{module}</cyan>:<cyan>{function}</cyan> - {message}",
    )
    _HANDLER_IDS.append(console_handler_id)

    # Register cleanup function to run at exit
    atexit.register(cleanup_logger)

    _IS_CONFIGURED = True  # Mark as configured


def log_exceptions(func):
    """Decorator to automatically log exceptions"""

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.opt(exception=True).error(f"Exception in {func.__name__}: {str(e)}")
            raise

    return wrapper


def get_logger(name=None):
    """Get configured logger instance"""
    global _HANDLER_IDS
    if not _IS_CONFIGURED:  # Use our flag instead of internal attribute
        configure_logger()

    # Add JSON formatting for production (only once)
    if os.getenv("LOG_ENV") == "PRODUCTION" and not any("app.json.log" in str(h) for h in _HANDLER_IDS):
        json_handler_id = logger.add(
            sink=os.path.join(log_path, "app.json.log"),
            serialize=True,
            format="{time:YYYY-MM-DD HH:mm:ss.SSS} | {level} | {module} | {function} | {line} | {message}",
        )
        _HANDLER_IDS = _HANDLER_IDS + [json_handler_id]  # Assign to make flake8 happy

    if name:
        logger.bind(name=name)

    return logger
