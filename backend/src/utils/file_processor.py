import io
import re
from datetime import datetime

import pandas as pd

from .logger import get_logger

logger = get_logger()


def read_csv_chunks(content, chunk_size=1000):
    """
    将 CSV 内容分块读取
    """
    try:
        return pd.read_csv(io.StringIO(content), chunksize=chunk_size)
    except Exception as e:
        raise ValueError(f"CSV 文件格式错误: {str(e)}")


def validate_columns(df, required_columns):
    """
    验证 DataFrame 是否包含所有必需的列
    """
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f'缺少必需的列: {", ".join(missing_columns)}')


def process_row(row, account_id):
    """
    处理 CSV 文件中的一行数据
    返回: (transaction_dict, error_dict, rsu_vesting_event, rsu_sell_info)
    """
    try:
        # 验证必需字段是否存在
        required_fields = ["Run Date", "Action", "Symbol", "Quantity"]
        # 检查是否为 CD 交易
        action = str(row["Action"]).strip().upper()
        if "CD" in action and "0%" in action:
            return None, None, None, None  # 直接跳过这类交易，不报错

        for field in required_fields:
            if field not in row or pd.isna(row[field]):
                logger.error(f"Missing required field: {field}")
                return None, {"row": row.name + 2, "error": "缺少必需字段", "reason": f'字段 "{field}" 不能为空'}, None, None

        # 解析日期 (MM/DD/YYYY 格式)
        try:
            date_str = str(row["Run Date"]).strip()
            trans_time = datetime.strptime(date_str, "%m/%d/%Y")
            trans_date = trans_time.strftime("%Y-%m-%d")
        except ValueError as e:
            logger.error(f"Date parsing error: {e}")
            return (
                None,
                {"row": row.name + 2, "error": "日期格式错误", "reason": f'日期 "{row["Run Date"]}" 格式应为 MM/DD/YYYY'},
                None,
                None,
            )

        # 验证交易类型
        action = str(row["Action"]).strip().upper()
        logger.info(f"Processing action: {action}")

        # 验证股票代码
        symbol = str(row["Symbol"]).strip().upper()
        if not symbol:
            logger.error("Empty symbol")
            return None, {"row": row.name + 2, "error": "无效的股票代码", "reason": "股票代码不能为空"}, None, None

        # 处理特殊情况: RSU 授予 (这种情况下通常没有价格)
        if "RSU" in action and "BOUGHT" in action:
            logger.info(f"Detected RSU vesting event: {symbol} on {trans_date}")
            try:
                quantity_str = str(row["Quantity"]).strip().replace(",", "")
                quantity = abs(float(quantity_str))

                rsu_vesting_event = {"symbol": symbol, "quantity": quantity, "trans_time": trans_date, "row": row.name + 2}
                return None, None, rsu_vesting_event, None
            except ValueError as e:
                logger.error(f"Error parsing RSU quantity: {e}")
                return (
                    None,
                    {"row": row.name + 2, "error": "RSU数量格式错误", "reason": f'数量 "{row["Quantity"]}" 格式无效'},
                    None,
                    None,
                )

        # 处理特殊情况: 反向拆分 (REVERSE SPLIT)
        if "REVERSE SPLIT" in action:
            logger.info(f"Detected REVERSE SPLIT transaction: {action}")

            # 识别是FROM还是TO记录
            is_from_record = "R/S FROM" in action
            is_to_record = "R/S TO" in action

            original_symbol_id = None
            new_symbol_id = None
            ticker = str(row["Symbol"]).strip().upper()  # Get ticker from Symbol column

            # 如果是TO记录，尝试从括号中再次提取原始ID (e.g., 25460E521 from '...(25460E521) (Cash)')
            if is_to_record:
                # Ensure new_id is extracted from TO record's action string first
                to_match = re.search(r"TO\s+([A-Z0-9]+)(?:#|\s)", action)
                if to_match:
                    new_symbol_id = to_match.group(1)
                    logger.info(f"Extracted New ID (from TO Action): {new_symbol_id}")
                else:
                    logger.debug(f"[TO Record Parsing] No match for new_symbol_id regex in action: '{action}'")

                # Try to extract original_id from parentheses before (Cash)
                # Use a more direct regex focusing on the parenthesized CUSIP
                original_from_paren_match = re.search(r"\(([A-Z0-9]{9})\)", action)  # UPDATED REGEX - More direct
                if original_from_paren_match:
                    original_symbol_id = original_from_paren_match.group(1).strip()
                    logger.info(f"Extracted Original ID (from TO parentheses): {original_symbol_id}")  # UPDATED LOG MESSAGE
                else:
                    logger.debug(f"[TO Record Parsing] No match for original_symbol_id regex in action: '{action}'")

                logger.debug(
                    f"[TO Record Parsing] Action: '{action}' -> Extracted Original ID: '{original_symbol_id}', Extracted New ID: '{new_symbol_id}', Ticker: '{ticker}'"
                )

            # 如果是FROM记录，只提取 original_id
            elif is_from_record:
                from_match = re.search(r"FROM\s+([A-Z0-9]+)(?:#|\s)", action)
                if from_match:
                    original_symbol_id = from_match.group(1)
                    logger.info(f"Extracted Original ID (from FROM Action): {original_symbol_id}")
                else:
                    logger.debug(f"[FROM Record Parsing] No match for original_symbol_id regex in action: '{action}'")
                # Do not attempt to extract new_id from FROM record
                new_symbol_id = None  # Explicitly set new_id to None for FROM records
                logger.debug(
                    f"[FROM Record Parsing] Action: '{action}' -> Extracted Original ID: '{original_symbol_id}', Extracted New ID: '{new_symbol_id}', Ticker: '{ticker}'"
                )

            # 提取数量
            quantity = 0
            try:
                quantity_str = str(row["Quantity"]).strip().replace(",", "")
                logger.debug(f"Attempting to parse quantity_str: '{quantity_str}'")
                quantity = float(quantity_str)
                logger.debug(f"Successfully parsed quantity: {quantity}")
                # Don't assume sign, keep as is from file (-500 for FROM, +25 for TO)
            except ValueError as e_qty:
                logger.error(f"Could not parse quantity '{quantity_str}' for reverse split row: {e_qty}")
                return (
                    None,
                    {
                        "row": row.name + 2,
                        "error": "Reverse Split数量格式错误",
                        "reason": f'数量 "{row["Quantity"]}" 格式无效: {e_qty}',
                    },
                    None,
                    None,
                )

            # Validation based on record type
            valid = False
            if is_from_record:
                if original_symbol_id:
                    valid = True
                else:
                    logger.warning(f"[FROM Validation Failed] Could not determine original ID. Action='{action}'")
            elif is_to_record:
                if original_symbol_id and new_symbol_id:
                    valid = True
                else:
                    logger.warning(
                        f"[TO Validation Failed] Could not determine both original and new IDs. Original='{original_symbol_id}', New='{new_symbol_id}'. Action='{action}'"
                    )
            else:
                logger.warning(f"Unknown REVERSE SPLIT record type. Action='{action}'")

            logger.debug(f"Reverse split validation result: valid={valid}")
            if not valid:
                logger.info("Skipping invalid reverse split record.")
                return None, None, None, None  # Skip invalid/incomplete records

            logger.debug("Validation passed, proceeding to create split component dictionary...")
            # 返回包含所有提取信息的组件字典
            split_component = {
                "type": "REVERSE_SPLIT_COMPONENT",
                "account_id": account_id,
                "trans_time": trans_date,
                "row": row.name + 2,
                "record_type": "FROM" if is_from_record else ("TO" if is_to_record else "UNKNOWN"),
                "ticker": ticker,  # Ticker from 'Symbol' column (e.g., YANG or 25460E521)
                "original_id": original_symbol_id,  # e.g., 25460E521
                "new_id": new_symbol_id,  # e.g., 25461A460
                "quantity": quantity,  # e.g., -500 or 25
            }

            logger.info(f"Created REVERSE SPLIT component: {split_component}")
            # Return the component, not a transaction, error, rsu_vesting, or rsu_sell
            return split_component, None, None, None

        # --- LOGIC TO DETERMINE TRANSACTION TYPE ---
        # 确定交易类型
        if "REINVESTMENT" in action and "CASH" not in row["Description"]:
            action_type = "BUY"  # Reinvestments are essentially buys
            multiplier = 1
            logger.info(f"Detected REINVESTMENT transaction: {action}")
        elif "YOU SOLD" in action:
            action_type = "SELL"
            # 处理量为负数
            multiplier = -1
        elif "YOU BOUGHT" in action:
            action_type = "BUY"
            multiplier = 1
        elif "EXPIRED" in action:
            action_type = "EXPIRED"
            price = 0
            multiplier = -1
        else:
            # If it's not RSU, SPLIT, BUY, or SELL, it's likely an unhandled type
            logger.warning(f"Unhandled action type encountered: {action}")
            return (
                None,
                {"row": row.name + 2, "error": "无效的交易类型", "reason": f'交易类型 "{action}" 无法识别或不支持'},
                None,
                None,
            )
        # --- LOGIC ---

        # 解析数量和价格
        try:
            # 处理可能的科学记数法和逗号
            quantity_str = str(row["Quantity"]).strip().replace(",", "")

            # 处理科学记数法
            if "e" in quantity_str.lower():
                quantity = float(quantity_str)
            else:
                quantity = float(quantity_str)

            # 根据交易类型调整数量的符号
            quantity = abs(quantity) * multiplier

            # 验证价格 - 对于卖出必须有价格
            if "Price" not in row or pd.isna(row["Price"]) or str(row["Price"]).strip() == "":
                if action_type == "EXPIRED":
                    price = 0
                    logger.info("Using default price 0 for expired transaction")
                elif action_type == "SELL":
                    logger.error("Missing price for sell transaction")
                    return (
                        None,
                        {"row": row.name + 2, "error": "卖出交易需要价格", "reason": "卖出交易的价格不能为空"},
                        None,
                        None,
                    )

                # 对于其他类型交易，如果没有价格但有Amount, 尝试计算
                if "Amount" in row and not pd.isna(row["Amount"]) and quantity != 0:
                    try:
                        amount_str = str(row["Amount"]).strip().replace(",", "")
                        amount = float(amount_str)
                        price = abs(amount / quantity)
                        logger.info(f"Calculated price from amount: {price}")
                    except (ValueError, ZeroDivisionError) as e:
                        logger.error(f"Cannot calculate price from amount: {e}")
                        return (
                            None,
                            {"row": row.name + 2, "error": "无法计算价格", "reason": f"无法从金额计算价格: {e}"},
                            None,
                            None,
                        )
                else:
                    # 特殊处理: CASH 使用默认价格 1
                    if symbol == "CASH" or symbol == "315994103":
                        price = 1
                        logger.info(f"Using default price 1 for {symbol}")
                    else:
                        logger.error("Missing price and cannot calculate")
                        return (
                            None,
                            {"row": row.name + 2, "error": "缺少价格", "reason": "价格不能为空且无法从其他字段计算"},
                            None,
                            None,
                        )
            else:
                # 正常解析价格
                price_str = str(row["Price"]).strip().replace(",", "")

                if price_str == "":
                    if symbol == "CASH":
                        price = 1
                        logger.info(f"Using default price 1 for {symbol}")
                    else:
                        logger.error("Empty price")
                        return None, {"row": row.name + 2, "error": "价格为空", "reason": "价格字段不能为空"}, None, None
                else:
                    if "e" in price_str.lower():
                        price = float(price_str)
                    else:
                        price = float(price_str)

            logger.info(f"Parsed quantity: {quantity}, price: {price}")

        except ValueError as e:
            logger.error(f"Number parsing error: {e}")
            return (
                None,
                {
                    "row": row.name + 2,
                    "error": "数量或价格格式错误",
                    "reason": f'数量 "{row["Quantity"]}" 或价格 "{row["Price"]}" 格式无效',
                },
                None,
                None,
            )

        # 验证价格是否为正数
        if price < 0:
            logger.error(f"Invalid price: price={price}")
            return None, {"row": row.name + 2, "error": "无效的价格", "reason": f"价格 ({price}) 必须大于等于0"}, None, None

        # 创建交易记录
        transaction = {
            "account_id": account_id,
            "symbol": symbol,
            "quantity": quantity,
            "price": price,
            "trans_time": trans_date,
        }

        # 如果是卖出交易，创建RSU卖出信息用于后续匹配
        rsu_sell_info = None
        if action_type == "SELL":
            # Add a flag to indicate if this is a normal sell transaction
            # This ensures that only actual RSU-related sells are flagged
            # Default to False for all sell transactions
            is_rsu_related = False

            rsu_sell_info = {
                "symbol": symbol,
                "quantity": quantity,
                "price": price,
                "trans_time": trans_date,
                "is_rsu_related": is_rsu_related,  # Add this flag to distinguish regular sells from RSU sells
            }

        logger.info(f"Created transaction: {transaction}")
        return transaction, None, None, rsu_sell_info

    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return None, {"row": row.name + 2, "error": "处理行数据时出错", "reason": str(e)}, None, None
