import json
import os
import time
from pathlib import Path

import pandas as pd
import yfinance as yf

from .logger import get_logger, log_exceptions


def get_vix_ticker():
    """
    Get VIX ticker instance for market volatility data.

    Returns:
        yfinance.Ticker: VIX ticker object for fetching volatility data
    """
    return yf.Ticker("^VIX")


@log_exceptions
class StockCache:
    def __init__(self, cache_dir: str = None, cache_expiry: int = 24 * 60 * 60):
        # 使用绝对路径 - updated for new structure
        if cache_dir is None:
            cache_dir = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "cache"
            )
        self.cache_dir = Path(cache_dir)
        self.cache_expiry = cache_expiry
        self.cache_dir.mkdir(exist_ok=True)
        self.logger = get_logger()

    def get_historical_data(self, symbol: str) -> pd.DataFrame:
        """Get historical price data with local caching"""
        # Clean the symbol
        symbol = symbol.strip().replace('"', "").upper()
        cache_file = self.cache_dir / f"{symbol}_data.parquet"

        # Try to read from cache first
        if cache_file.exists():
            cache_time = cache_file.stat().st_mtime
            if time.time() - cache_time < self.cache_expiry:
                try:
                    data = pd.read_parquet(cache_file)
                    if isinstance(data.columns, pd.MultiIndex):
                        data.columns = data.columns.get_level_values("Price")
                    if not data.empty:
                        return data
                except Exception as e:
                    self.logger.error(f"Cache read error for {symbol}: {e}")

        # Download fresh data
        try:
            data = yf.download(symbol, period="1y", interval="1h", progress=False, auto_adjust=True)
        except Exception as e:
            self.logger.exception(f"Error downloading data for {symbol}: {e}")
            return pd.DataFrame()

        # Check if data is valid
        if data.empty:
            self.logger.error(f"No data available for {symbol}")
            return pd.DataFrame()

        # Process the data
        if isinstance(data.columns, pd.MultiIndex):
            data.columns = data.columns.get_level_values("Price")

        # Try to cache the data
        try:
            data.to_parquet(cache_file)
        except Exception as e:
            self.logger.error(f"Cache write error for {symbol}: {e}")

        return data

    def get_current_prices(self, symbols: list) -> dict:
        """Get current prices for multiple symbols with caching"""
        prices = {}

        for symbol in symbols:
            symbol = symbol.strip().replace('"', "").upper()
            cache_file = self.cache_dir / f"{symbol}_price.json"

            # Check cache first (5 minute expiry for current prices)
            if cache_file.exists():
                cache_time = cache_file.stat().st_mtime
                if time.time() - cache_time < 300:  # 5 minutes
                    try:
                        with open(cache_file, "r") as f:
                            cached_data = json.load(f)
                            prices[symbol] = cached_data["price"]
                            continue
                    except Exception as e:
                        self.logger.error(f"Price cache read error for {symbol}: {e}")

            # Fetch fresh price
            try:
                ticker = yf.Ticker(symbol)
                info = ticker.info

                # Try different price fields
                price = None
                for price_field in ["regularMarketPrice", "currentPrice", "previousClose"]:
                    if price_field in info and info[price_field]:
                        price = float(info[price_field])
                        break

                if price and price > 0:
                    prices[symbol] = price

                    # Cache the price
                    try:
                        with open(cache_file, "w") as f:
                            json.dump({"price": price, "timestamp": time.time()}, f)
                    except Exception as e:
                        self.logger.error(f"Price cache write error for {symbol}: {e}")

            except Exception as e:
                self.logger.error(f"Error fetching price for {symbol}: {e}")

        return prices
