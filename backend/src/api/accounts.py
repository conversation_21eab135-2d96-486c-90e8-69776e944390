from flask import jsonify, request

from ..utils.calculations import calculate_current_market_value, calculate_return_rate
from ..utils.logger import get_logger
from . import accounts_bp, get_db_connection

logger = get_logger()


@accounts_bp.route("/", methods=["GET"], strict_slashes=False)
def get_accounts():
    conn = get_db_connection()
    try:
        # 获取所有账户基本信息
        accounts = conn.execute(
            """
            SELECT account_id as id, name
            FROM accounts
            ORDER BY account_id
        """
        ).fetchall()

        result = []
        for account in accounts:
            account_dict = dict(account)
            # 为每个账户计算当前市值
            current_value, _, _ = calculate_current_market_value(conn, account_id=account["id"])
            account_dict["current_value"] = current_value
            result.append(account_dict)

        return jsonify(result)
    except Exception as e:
        logger.error(f"Error in get_accounts: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@accounts_bp.route("/", methods=["POST"], strict_slashes=False)
def create_account():
    conn = get_db_connection()
    try:
        data = request.json
        if not data or "name" not in data:
            return jsonify({"error": "Missing required fields"}), 400

        cursor = conn.execute("INSERT INTO accounts (name) VALUES (?)", (data["name"],))
        account_id = cursor.lastrowid

        # 使用通用函数计算市值和收益率
        current_value, total_cost, return_rate = calculate_current_market_value(conn, account_id=account_id)

        # 获取账户基本信息
        account = conn.execute(
            """
            SELECT account_id, name
            FROM accounts
            WHERE account_id = ?
        """,
            [account_id],
        ).fetchone()

        conn.commit()
        return (
            jsonify(
                {
                    "account_id": account["account_id"],
                    "name": account["name"],
                    "initial_capital": 0,
                    "current_value": current_value,
                    "return_rate": return_rate,
                }
            ),
            201,
        )
    except Exception as e:
        conn.rollback()
        logger.error(f"Error in create_account: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()


@accounts_bp.route("/<int:account_id>", methods=["DELETE"], strict_slashes=False)
def delete_account(account_id):
    if not account_id or account_id <= 0:
        return jsonify({"error": "Invalid account ID"}), 400

    conn = get_db_connection()
    try:
        # 检查账户是否存在
        account = conn.execute("SELECT * FROM accounts WHERE account_id = ?", (account_id,)).fetchone()
        if not account:
            return jsonify({"error": "Account not found"}), 404

        # 首先删除账户相关的所有交易记录
        conn.execute("DELETE FROM transactions WHERE account_id = ?", (account_id,))
        # 删除账户的已实现收益记录
        conn.execute("DELETE FROM realized_gains WHERE account_id = ?", (account_id,))
        # 删除账户的每日收益记录
        conn.execute("DELETE FROM daily_returns WHERE account_id = ?", (account_id,))
        # 最后删除账户
        conn.execute("DELETE FROM accounts WHERE account_id = ?", (account_id,))
        conn.commit()
        return jsonify({"status": "success", "message": "Account deleted successfully"}), 200
    except Exception as e:
        conn.rollback()
        logger.error(f"Error in delete_account: {str(e)}")
        return jsonify({"error": str(e)}), 500
    finally:
        conn.close()
