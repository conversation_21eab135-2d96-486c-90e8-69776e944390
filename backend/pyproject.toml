[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "stock-analysis-backend"
version = "1.0.0"
description = "Stock trading analysis backend service"
authors = [
    {name = "WAIBIBABU"}
]
dependencies = [
    "flask==3.0.0",
    "flask-cors==4.0.0",
    "yfinance==0.2.63",
    "pandas==2.2.0",
    "numpy==1.26.2",
    "python-dotenv==1.0.0",
    "gunicorn==21.2.0",
    "requests==2.31.0",
    "SQLAlchemy==2.0.23",
    "tenacity>=8.0.1",
    "loguru==0.7.3",
    "schedule==1.2.2",
    "torch==2.7.0",
    "torchvision==0.22.0",
    "optuna==3.3.0",
    "plotnine==0.14.5",
    "matplotlib==3.8.0",
    "mizani>=0.13.0",
    "pandas_ta==0.3.14b0",
    "scikit-learn==1.3.2",
    "seaborn==0.13.0",
    "pyarrow==14.0.0",
    "yahooquery==2.4.1",
    "exchange-calendars==4.10.1",
    "scipy>=1.9.0",
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "prophet==1.1.7",
    "openai==1.96.1"
]
requires-python = ">=3.8"

[tool.pytest.ini_options]
# Test discovery
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]

# Default options
addopts = [
    "--verbose",
    "--cov=src",
    "--cov-report=html:htmlcov",
    "--cov-report=term-missing", 
    "--cov-report=xml",
    "--cov-fail-under=75",
    "--tb=short",
    "--strict-markers",
    "--strict-config",
    "--durations=10",
    "-ra"
]

# Test markers for categorization and filtering
markers = [
    "unit: Unit tests for individual functions/methods",
    "integration: Integration tests across multiple components", 
    "slow: Slow running tests (>1 second)",
    "api: API endpoint tests",
    "database: Database related tests",
    "external: Tests that require external services (should be mocked)",
    "options: Options analysis specific tests",
    "strategies: Strategy analysis tests", 
    "data: Data fetching and management tests",
    "watchlist: Watchlist management tests",
    "config: Configuration management tests"
]

# Warning filters
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::PendingDeprecationWarning",
    "ignore:pkg_resources is deprecated:UserWarning",
    "ignore::UserWarning:yahooquery",
    "ignore::FutureWarning:pandas"
]

# Minimum pytest version
minversion = "6.0"

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
    "*/migrations/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:"
]
show_missing = true
skip_covered = false
precision = 2

[tool.coverage.html]
directory = "htmlcov"

[tool.black]
line-length = 127
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
line_length = 127
multi_line_output = 3
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true
skip_glob = ["*/migrations/*", "*/venv/*", "*/.venv/*"]

[tool.pylint.messages_control]
disable = [
    "C0114",  # missing-module-docstring
    "C0115",  # missing-class-docstring
    "C0116",  # missing-function-docstring
    "R0903",  # too-few-public-methods
    "R0913",  # too-many-arguments
    "W0613",  # unused-argument
    "C0103",  # invalid-name
]

[tool.pylint.format]
max-line-length = 127

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = false
disallow_incomplete_defs = false
check_untyped_defs = true
disallow_untyped_decorators = false
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
ignore_missing_imports = true
