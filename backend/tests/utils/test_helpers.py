"""
Test helper utilities for the stock trading application.
"""

import json
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from typing import Any, Dict, List

import numpy as np
import pandas as pd


class DatabaseTestHelper:
    """Helper class for database operations in tests."""

    @staticmethod
    def get_connection(db_path: str) -> sqlite3.Connection:
        """Get a database connection for testing."""
        conn = sqlite3.connect(db_path)
        conn.row_factory = sqlite3.Row
        return conn

    @staticmethod
    def create_test_account(
        conn: sqlite3.Connection, account_id: str = "TEST001", account_name: str = "Test Account"
    ) -> Dict[str, Any]:
        """Create a test account in the database."""
        account_data = {
            "account_id": account_id,
            "account_name": account_name,
            "account_type": "Individual",
            "created_at": datetime.now().isoformat(),
        }

        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO accounts (name) VALUES (?)
        """,
            (account_data["account_name"],),
        )
        account_data["account_id"] = cursor.lastrowid
        conn.commit()

        return account_data

    @staticmethod
    def create_test_transaction(
        conn: sqlite3.Connection,
        account_id: str = "TEST001",
        symbol: str = "AAPL",
        transaction_type: str = "BUY",
        quantity: int = 100,
        price: float = 150.0,
    ) -> Dict[str, Any]:
        """Create a test transaction in the database."""
        # Adjust quantity for sell transactions (negative quantity)
        if transaction_type == "SELL":
            quantity = -abs(quantity)

        trans_time = datetime.now().isoformat()
        transaction_data = {
            "account_id": account_id,
            "symbol": symbol,
            "quantity": quantity,
            "price": price,
            "trans_time": trans_time,
        }

        cursor = conn.cursor()
        cursor.execute(
            """
            INSERT INTO transactions (account_id, symbol, quantity, price, trans_time)
            VALUES (?, ?, ?, ?, ?)
        """,
            (
                transaction_data["account_id"],
                transaction_data["symbol"],
                transaction_data["quantity"],
                transaction_data["price"],
                transaction_data["trans_time"],
            ),
        )
        transaction_data["transaction_id"] = cursor.lastrowid
        conn.commit()

        return transaction_data

    @staticmethod
    def create_multiple_transactions(
        conn: sqlite3.Connection, account_id: str = "TEST001", symbols: List[str] = None
    ) -> List[Dict[str, Any]]:
        """Create multiple test transactions."""
        if symbols is None:
            symbols = ["AAPL", "GOOGL", "MSFT", "TSLA"]

        transactions = []
        for i, symbol in enumerate(symbols):
            transaction = DatabaseTestHelper.create_test_transaction(
                conn, account_id, symbol, "BUY", quantity=100 + i * 10, price=100.0 + i * 50
            )
            transactions.append(transaction)

        return transactions

    @staticmethod
    def get_account_count(conn: sqlite3.Connection) -> int:
        """Get the number of accounts in the database."""
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM accounts")
        return cursor.fetchone()[0]

    @staticmethod
    def get_transaction_count(conn: sqlite3.Connection) -> int:
        """Get the number of transactions in the database."""
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM transactions")
        return cursor.fetchone()[0]


class APITestHelper:
    """Helper class for API testing."""

    @staticmethod
    def assert_json_response(response, expected_status: int = 200):
        """Assert that response is JSON with expected status."""
        assert response.status_code == expected_status
        assert response.content_type == "application/json"
        return response.get_json()

    @staticmethod
    def assert_success_response(response, expected_status: int = 200):
        """Assert that response is successful with expected status."""
        assert response.status_code == expected_status
        data = response.get_json()
        return data

    @staticmethod
    def assert_error_response(response, expected_status: int, expected_message: str = None):
        """Assert that response is an error with expected status and message."""
        assert response.status_code == expected_status
        data = response.get_json()
        assert "error" in data
        if expected_message:
            assert expected_message in data["error"]
        return data

    @staticmethod
    def create_account_via_api(client, account_data: Dict[str, Any]):
        """Create an account via API."""
        return client.post("/api/accounts", data=json.dumps(account_data), content_type="application/json")

    @staticmethod
    def create_transaction_via_api(client, transaction_data: Dict[str, Any]):
        """Create a transaction via API."""
        return client.post("/api/transactions", data=json.dumps(transaction_data), content_type="application/json")


class MockDataGenerator:
    """Generate mock data for testing."""

    @staticmethod
    def generate_stock_prices(symbol: str, days: int = 30, start_price: float = 100.0) -> pd.DataFrame:
        """Generate mock stock price data."""
        dates = pd.date_range(start=datetime.now() - timedelta(days=days), end=datetime.now(), freq="D")

        # Generate realistic price movements
        returns = np.random.normal(0.001, 0.02, len(dates))  # Daily returns
        prices = [start_price]

        for ret in returns[1:]:
            prices.append(prices[-1] * (1 + ret))

        return pd.DataFrame(
            {
                "Open": [p * np.random.uniform(0.99, 1.01) for p in prices],
                "High": [p * np.random.uniform(1.00, 1.05) for p in prices],
                "Low": [p * np.random.uniform(0.95, 1.00) for p in prices],
                "Close": prices,
                "Volume": np.random.randint(1000000, 10000000, len(dates)),
            },
            index=dates,
        )

    @staticmethod
    def generate_portfolio_data(symbols: List[str] = None) -> List[Dict[str, Any]]:
        """Generate mock portfolio data."""
        if symbols is None:
            symbols = ["AAPL", "GOOGL", "MSFT", "TSLA", "AMZN"]

        portfolio = []
        for symbol in symbols:
            quantity = np.random.randint(10, 200)
            avg_cost = np.random.uniform(50, 300)
            current_price = avg_cost * np.random.uniform(0.8, 1.3)
            market_value = quantity * current_price
            unrealized_gain = market_value - (quantity * avg_cost)
            unrealized_gain_pct = (unrealized_gain / (quantity * avg_cost)) * 100

            portfolio.append(
                {
                    "symbol": symbol,
                    "quantity": quantity,
                    "avg_cost": round(avg_cost, 2),
                    "current_price": round(current_price, 2),
                    "market_value": round(market_value, 2),
                    "unrealized_gain": round(unrealized_gain, 2),
                    "unrealized_gain_pct": round(unrealized_gain_pct, 2),
                }
            )

        return portfolio

    @staticmethod
    def generate_market_data() -> Dict[str, Any]:
        """Generate mock market data."""
        return {
            "market_cap": np.random.uniform(1e12, 3e12),
            "pe_ratio": np.random.uniform(15, 30),
            "dividend_yield": np.random.uniform(0.01, 0.05),
            "beta": np.random.uniform(0.5, 2.0),
            "volume": np.random.randint(1000000, 100000000),
            "avg_volume": np.random.randint(5000000, 50000000),
        }


class AssertionHelper:
    """Helper class for common test assertions."""

    @staticmethod
    def assert_portfolio_structure(portfolio_data: List[Dict[str, Any]]):
        """Assert that portfolio data has the expected structure."""
        assert isinstance(portfolio_data, list)
        for item in portfolio_data:
            required_fields = [
                "symbol",
                "quantity",
                "avg_cost",
                "current_price",
                "market_value",
                "unrealized_gain",
                "unrealized_gain_pct",
            ]
            for field in required_fields:
                assert field in item, f"Missing field: {field}"
                assert item[field] is not None, f"Field {field} is None"

    @staticmethod
    def assert_transaction_structure(transaction_data: Dict[str, Any]):
        """Assert that transaction data has the expected structure."""
        required_fields = ["account_id", "symbol", "transaction_type", "quantity", "price", "transaction_date"]
        for field in required_fields:
            assert field in transaction_data, f"Missing field: {field}"
            assert transaction_data[field] is not None, f"Field {field} is None"

    @staticmethod
    def assert_account_structure(account_data: Dict[str, Any]):
        """Assert that account data has the expected structure."""
        required_fields = ["account_id", "account_name", "account_type"]
        for field in required_fields:
            assert field in account_data, f"Missing field: {field}"
            assert account_data[field] is not None, f"Field {field} is None"

    @staticmethod
    def assert_numeric_range(value: float, min_val: float, max_val: float, field_name: str = "value"):
        """Assert that a numeric value is within the expected range."""
        assert isinstance(value, (int, float)), f"{field_name} must be numeric"
        assert min_val <= value <= max_val, f"{field_name} {value} not in range [{min_val}, {max_val}]"
