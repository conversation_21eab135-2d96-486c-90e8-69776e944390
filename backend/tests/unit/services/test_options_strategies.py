"""
Comprehensive test suite for options strategies analysis.

Tests all strategy analysis algorithms including:
- Cash-Secured Puts analysis
- Covered Calls analysis
- Iron Condors analysis
- Win Rate Score calculations
- Edge cases and error handling
"""

from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import pytest

from src.services.options_strategies import OptionsStrategiesAnalyzer


@pytest.mark.unit
@pytest.mark.options
@pytest.mark.strategies
class TestOptionsStrategiesAnalyzer:
    """Test the OptionsStrategiesAnalyzer functionality."""

    @pytest.fixture
    def analyzer(self):
        """Create an OptionsStrategiesAnalyzer instance for testing."""
        return OptionsStrategiesAnalyzer()

    @pytest.fixture
    def comprehensive_options_data(self):
        """Create comprehensive options data for testing all strategies."""
        base_date = datetime.now() + timedelta(days=30)
        expiration = base_date.strftime("%Y-%m-%d")

        data = []

        # AAPL options (current price: 150.0)
        aapl_strikes_puts = [130, 135, 140, 145, 148, 150]
        aapl_strikes_calls = [150, 152, 155, 160, 165, 170, 175, 180]

        for strike in aapl_strikes_puts:
            data.append(
                {
                    "symbol": "AAPL",
                    "optionType": "puts",
                    "strike": strike,
                    "expiration": expiration,
                    "dte": 30,
                    "bid": max(0.1, (150 - strike) * 0.6 + np.random.uniform(0.5, 2.0)),
                    "ask": max(0.2, (150 - strike) * 0.6 + np.random.uniform(1.0, 2.5)),
                    "impliedVolatility": 0.20 + np.random.uniform(0.05, 0.15),
                    "volume": np.random.randint(50, 500),
                    "openInterest": np.random.randint(100, 1000),
                }
            )

        for strike in aapl_strikes_calls:
            data.append(
                {
                    "symbol": "AAPL",
                    "optionType": "calls",
                    "strike": strike,
                    "expiration": expiration,
                    "dte": 30,
                    "bid": max(0.1, max(0, 150 - strike) + np.random.uniform(0.5, 3.0)),
                    "ask": max(0.2, max(0, 150 - strike) + np.random.uniform(1.0, 3.5)),
                    "impliedVolatility": 0.18 + np.random.uniform(0.05, 0.12),
                    "volume": np.random.randint(30, 400),
                    "openInterest": np.random.randint(80, 800),
                }
            )

        return pd.DataFrame(data)

    @pytest.fixture
    def sample_prices(self):
        """Create sample current stock prices."""
        return {"AAPL": 150.0, "MSFT": 300.0, "GOOGL": 2500.0}

    @pytest.fixture
    def default_put_params(self):
        """Default parameters for cash-secured puts testing."""
        return {
            "min_dte": 20,
            "max_dte": 60,
            "min_annual_roi": 0.10,
            "min_buffer_percent": 0.02,
            "max_delta": 0.35,
            "sort_by": "winRateScore",
            "max_candidates": 10,
        }

    @pytest.fixture
    def default_call_params(self):
        """Default parameters for covered calls testing."""
        return {
            "min_dte": 20,
            "max_dte": 60,
            "min_annual_roi": 0.08,
            "min_upside_buffer": 0.01,
            "max_delta": 0.35,
            "sort_by": "annualizedRoi",
            "max_candidates": 10,
        }

    def test_cash_secured_puts_basic_analysis(self, analyzer, comprehensive_options_data, sample_prices, default_put_params):
        """Test basic cash-secured puts analysis functionality."""
        candidates = analyzer.find_cash_secured_put_candidates(comprehensive_options_data, sample_prices, default_put_params)

        # Should find some candidates
        assert not candidates.empty

        # All candidates should be puts
        assert all(candidates["optionType"] == "puts")

        # All strikes should be below current price (OTM puts)
        assert all(candidates["strike"] < sample_prices["AAPL"])

        # DTE should be within specified range
        assert all(candidates["dte"] >= default_put_params["min_dte"])
        assert all(candidates["dte"] <= default_put_params["max_dte"])

        # Required columns should exist
        required_columns = [
            "symbol",
            "currentPrice",
            "strike",
            "expiration",
            "dte",
            "bid",
            "premium",
            "maxGain",
            "maxLoss",
            "annualizedRoi",
            "bufferPercent",
            "winRateScore",
        ]
        for col in required_columns:
            assert col in candidates.columns

        # Validate calculated fields
        assert all(candidates["premium"] == candidates["bid"] * 100)
        assert all(candidates["maxGain"] == candidates["premium"])
        assert all(candidates["bufferPercent"] >= default_put_params["min_buffer_percent"])
        assert all(candidates["annualizedRoi"] >= default_put_params["min_annual_roi"])

    def test_cash_secured_puts_empty_data(self, analyzer, sample_prices, default_put_params):
        """Test cash-secured puts analysis with empty data."""
        empty_data = pd.DataFrame()

        candidates = analyzer.find_cash_secured_put_candidates(empty_data, sample_prices, default_put_params)

        assert candidates.empty

    def test_cash_secured_puts_no_puts(self, analyzer, sample_prices, default_put_params):
        """Test cash-secured puts analysis with no put options."""
        # Create data with only calls
        calls_only_data = pd.DataFrame(
            {
                "symbol": ["AAPL", "AAPL"],
                "optionType": ["calls", "calls"],
                "strike": [155.0, 160.0],
                "expiration": ["2024-02-16", "2024-02-16"],
                "dte": [30, 30],
                "bid": [2.0, 1.0],
                "ask": [2.5, 1.5],
                "impliedVolatility": [0.25, 0.23],
                "volume": [100, 50],
                "openInterest": [500, 300],
            }
        )

        candidates = analyzer.find_cash_secured_put_candidates(calls_only_data, sample_prices, default_put_params)

        assert candidates.empty

    def test_covered_calls_basic_analysis(self, analyzer, comprehensive_options_data, sample_prices, default_call_params):
        """Test basic covered calls analysis functionality."""
        candidates = analyzer.find_covered_call_candidates(comprehensive_options_data, sample_prices, default_call_params)

        # Should find some candidates
        assert not candidates.empty

        # All candidates should be calls
        assert all(candidates["optionType"] == "calls")

        # All strikes should be at or above current price (ATM/OTM calls)
        assert all(candidates["strike"] >= sample_prices["AAPL"])

        # DTE should be within specified range
        assert all(candidates["dte"] >= default_call_params["min_dte"])
        assert all(candidates["dte"] <= default_call_params["max_dte"])

        # Required columns should exist
        required_columns = [
            "symbol",
            "currentPrice",
            "strike",
            "expiration",
            "dte",
            "bid",
            "premium",
            "maxGain",
            "annualizedRoi",
            "upsideBufferPercent",
            "winRateScore",
        ]
        for col in required_columns:
            assert col in candidates.columns

        # Validate calculated fields
        assert all(candidates["premium"] == candidates["bid"] * 100)
        assert all(candidates["maxGain"] == candidates["premium"])
        assert all(candidates["upsideBufferPercent"] >= default_call_params["min_upside_buffer"])
        assert all(candidates["annualizedRoi"] >= default_call_params["min_annual_roi"])

    def test_win_rate_score_calculation_puts(self, analyzer):
        """Test win rate score calculation for puts with known data."""
        # Create test data with known values
        test_data = pd.DataFrame(
            {
                "bufferPercent": [0.05, 0.10, 0.15],
                "dte": [30, 45, 60],
                "impliedVolatility": [0.20, 0.30, 0.40],
                "bid": [2.0, 3.0, 4.0],
                "ask": [2.2, 3.3, 4.4],
            }
        )

        scores = analyzer._calculate_put_win_rate_score(test_data)

        # Should return a Series with same length
        assert len(scores) == len(test_data)
        assert isinstance(scores, pd.Series)

        # All scores should be between 0 and 100
        assert all(scores >= 0)
        assert all(scores <= 100)

        # Higher buffer should generally give higher score
        # (though this might not always be true due to other factors)
        assert isinstance(scores.iloc[0], (int, float))

    def test_win_rate_score_calculation_calls(self, analyzer):
        """Test win rate score calculation for calls with known data."""
        test_data = pd.DataFrame(
            {
                "upsideBufferPercent": [0.02, 0.05, 0.10],
                "dte": [30, 45, 60],
                "impliedVolatility": [0.20, 0.30, 0.40],
                "bid": [2.0, 3.0, 4.0],
                "ask": [2.2, 3.3, 4.4],
            }
        )

        scores = analyzer._calculate_call_win_rate_score(test_data)

        # Should return a Series with same length
        assert len(scores) == len(test_data)
        assert isinstance(scores, pd.Series)

        # All scores should be between 0 and 100
        assert all(scores >= 0)
        assert all(scores <= 100)

    def test_dte_categorization(self, analyzer):
        """Test DTE categorization logic with edge cases."""
        test_cases = [
            (1, "Short"),
            (21, "Short"),
            (22, "Optimal"),
            (35, "Optimal"),
            (36, "Extended"),
            (50, "Extended"),
            (51, "Long"),
            (100, "Long"),
            (365, "Long"),
        ]

        for dte, expected_category in test_cases:
            category = analyzer._categorize_dte(dte)
            assert category == expected_category, f"DTE {dte} should be {expected_category}, got {category}"

    def test_delta_estimation_puts(self, analyzer):
        """Test put delta estimation with realistic data."""
        test_data = pd.DataFrame(
            {
                "currentPrice": [150.0, 150.0, 150.0],
                "strike": [140.0, 145.0, 150.0],
                "dte": [30, 30, 30],
                "impliedVolatility": [0.25, 0.25, 0.25],
            }
        )

        deltas = analyzer._estimate_put_delta(test_data)

        # Should return negative deltas for puts
        assert all(deltas <= 0)

        # OTM puts should have deltas closer to 0, ATM puts closer to -0.5
        assert deltas.iloc[0] > deltas.iloc[1]  # 140 strike > 145 strike
        assert deltas.iloc[1] > deltas.iloc[2]  # 145 strike > 150 strike

    def test_delta_estimation_calls(self, analyzer):
        """Test call delta estimation with realistic data."""
        test_data = pd.DataFrame(
            {
                "currentPrice": [150.0, 150.0, 150.0],
                "strike": [150.0, 155.0, 160.0],
                "dte": [30, 30, 30],
                "impliedVolatility": [0.25, 0.25, 0.25],
            }
        )

        deltas = analyzer._estimate_call_delta(test_data)

        # Should return positive deltas for calls
        assert all(deltas >= 0)

        # ATM calls should have higher deltas than OTM calls
        assert deltas.iloc[0] > deltas.iloc[1]  # 150 strike > 155 strike
        assert deltas.iloc[1] > deltas.iloc[2]  # 155 strike > 160 strike

    def test_adaptive_filtering_low_priced_stocks(self, analyzer, default_put_params):
        """Test adaptive filtering for low-priced stocks like TSLL."""
        # Create TSLL-like data (low-priced stock)
        tsll_data = pd.DataFrame(
            {
                "symbol": ["TSLL"] * 6,
                "optionType": ["puts"] * 6,
                "strike": [9.0, 10.0, 11.0, 12.0, 13.0, 14.0],
                "expiration": ["2024-02-16"] * 6,
                "dte": [35] * 6,
                "bid": [0.15, 0.25, 0.45, 0.75, 1.20, 1.80],
                "ask": [0.20, 0.30, 0.50, 0.80, 1.25, 1.85],
                "impliedVolatility": [0.35] * 6,
                "volume": [50] * 6,
                "openInterest": [200] * 6,
            }
        )

        tsll_prices = {"TSLL": 12.40}

        # Test with standard parameters (should be restrictive)
        standard_params = default_put_params.copy()
        standard_params["min_buffer_percent"] = 0.08  # 8%

        candidates = analyzer.find_cash_secured_put_candidates(tsll_data, tsll_prices, standard_params)

        # Should find the best candidate with adaptive filtering
        assert not candidates.empty
        assert len(candidates) == 1  # One best candidate per symbol

        # Verify adaptive buffer logic worked
        candidate = candidates.iloc[0]
        # With adaptive filtering, 3% buffer allows strikes that would be excluded with 8% buffer
        # For TSLL at $12.40, 3% buffer allows strikes above $12.03, while 8% buffer requires strikes below $11.40
        buffer_percent = candidate["bufferPercent"]
        assert buffer_percent >= 0.03  # Should meet adaptive threshold

        # The selected strike should be reasonable for the adaptive filtering
        assert candidate["strike"] < 12.40  # OTM put
        assert candidate["strike"] > 8.0  # Reasonable lower bound

    def test_diagnostic_analysis(self, analyzer, sample_prices, default_put_params):
        """Test the diagnostic analysis functionality."""
        # Create restrictive scenario
        restrictive_data = pd.DataFrame(
            {
                "symbol": ["AAPL"] * 4,
                "optionType": ["puts"] * 4,
                "strike": [140.0, 145.0, 148.0, 149.0],  # Very close to current price
                "expiration": ["2024-02-16"] * 4,
                "dte": [15, 30, 45, 60],  # Mix of DTEs
                "bid": [0.50, 1.00, 2.00, 3.00],
                "ask": [0.55, 1.05, 2.05, 3.05],
                "impliedVolatility": [0.25] * 4,
                "volume": [50] * 4,
                "openInterest": [200] * 4,
            }
        )

        # Use restrictive parameters
        restrictive_params = {
            "min_dte": 25,
            "max_dte": 50,
            "min_buffer_percent": 0.10,  # 10% - very restrictive
            "min_annual_roi": 0.20,  # 20% - very restrictive
        }

        diagnostic = analyzer.analyze_filtering_impact(
            restrictive_data, sample_prices, restrictive_params, "cash_secured_puts"
        )

        # Should provide diagnostic information
        assert "filter_impact" in diagnostic
        assert "suggestions" in diagnostic
        assert "parameter_recommendations" in diagnostic

        # Should identify restrictive filters
        assert len(diagnostic["suggestions"]) > 0

    def test_edge_case_zero_bid_options(self, analyzer, sample_prices, default_put_params):
        """Test handling of options with zero bid prices."""
        zero_bid_data = pd.DataFrame(
            {
                "symbol": ["AAPL"] * 3,
                "optionType": ["puts"] * 3,
                "strike": [140.0, 145.0, 148.0],
                "expiration": ["2024-02-16"] * 3,
                "dte": [30] * 3,
                "bid": [0.0, 0.01, 1.00],  # Include zero bid
                "ask": [0.05, 0.06, 1.05],
                "impliedVolatility": [0.25] * 3,
                "volume": [0, 1, 50],
                "openInterest": [0, 10, 200],
            }
        )

        candidates = analyzer.find_cash_secured_put_candidates(zero_bid_data, sample_prices, default_put_params)

        # Should filter out zero bid options
        if not candidates.empty:
            assert all(candidates["bid"] > 0)

    def test_extreme_dte_scenarios(self, analyzer, sample_prices, default_put_params):
        """Test handling of extreme DTE scenarios."""
        extreme_dte_data = pd.DataFrame(
            {
                "symbol": ["AAPL"] * 5,
                "optionType": ["puts"] * 5,
                "strike": [140.0, 142.0, 144.0, 146.0, 148.0],
                "expiration": ["2024-02-16"] * 5,
                "dte": [1, 7, 90, 180, 365],  # Very short to very long
                "bid": [0.10, 0.50, 2.00, 4.00, 8.00],
                "ask": [0.15, 0.55, 2.05, 4.05, 8.05],
                "impliedVolatility": [0.25] * 5,
                "volume": [50] * 5,
                "openInterest": [200] * 5,
            }
        )

        # Test with wide DTE range
        wide_dte_params = default_put_params.copy()
        wide_dte_params["min_dte"] = 1
        wide_dte_params["max_dte"] = 365

        candidates = analyzer.find_cash_secured_put_candidates(extreme_dte_data, sample_prices, wide_dte_params)

        # Should handle extreme DTEs gracefully
        if not candidates.empty:
            assert all(candidates["dte"] >= 1)
            assert all(candidates["dte"] <= 365)

    def test_high_volatility_environment(self, analyzer, sample_prices, default_put_params):
        """Test win rate scoring in high volatility environment."""
        high_iv_data = pd.DataFrame(
            {
                "symbol": ["AAPL"] * 3,
                "optionType": ["puts"] * 3,
                "strike": [140.0, 145.0, 148.0],
                "expiration": ["2024-02-16"] * 3,
                "dte": [30] * 3,
                "bid": [2.00, 3.00, 4.00],
                "ask": [2.05, 3.05, 4.05],
                "impliedVolatility": [0.80, 0.85, 0.90],  # Very high IV
                "volume": [100] * 3,
                "openInterest": [500] * 3,
            }
        )

        candidates = analyzer.find_cash_secured_put_candidates(high_iv_data, sample_prices, default_put_params)

        if not candidates.empty:
            # In high IV environment, buffer should be weighted more heavily
            assert "winRateScore" in candidates.columns
            assert all(candidates["winRateScore"] >= 0)
            assert all(candidates["winRateScore"] <= 100)

    def test_roi_calculation_accuracy(self, analyzer, sample_prices, default_put_params):
        """Test accuracy of ROI calculations."""
        test_data = pd.DataFrame(
            {
                "symbol": ["AAPL"],
                "optionType": ["puts"],
                "strike": [145.0],
                "expiration": ["2024-02-16"],
                "dte": [30],
                "bid": [2.00],
                "ask": [2.05],
                "impliedVolatility": [0.25],
                "volume": [100],
                "openInterest": [500],
            }
        )

        candidates = analyzer.find_cash_secured_put_candidates(test_data, sample_prices, default_put_params)

        if not candidates.empty:
            # Verify ROI calculation: (bid / strike) * (365 / dte)
            expected_roi = (2.00 / 145.0) * (365 / 30)
            actual_roi = candidates.iloc[0]["annualizedRoi"]

            # Allow small floating point differences
            assert abs(actual_roi - expected_roi) < 0.001


@pytest.mark.unit
@pytest.mark.options
@pytest.mark.integration
class TestOptionsStrategiesIntegration:
    """Integration tests for complete options strategies workflow."""

    @pytest.fixture
    def comprehensive_options_data(self):
        """Create comprehensive options data for integration testing."""
        base_date = datetime.now() + timedelta(days=30)
        expiration = base_date.strftime("%Y-%m-%d")

        # Create realistic options chain for TSLL
        data = []
        strikes = [8.0, 9.0, 10.0, 11.0, 12.0, 13.0, 14.0, 15.0, 16.0]

        for strike in strikes:
            # Put options
            data.append(
                {
                    "symbol": "TSLL",
                    "optionType": "puts",
                    "strike": strike,
                    "expiration": expiration,
                    "dte": 35,
                    "bid": max(0.05, (12.40 - strike) * 0.8 + 0.1),  # Realistic bid based on intrinsic value
                    "ask": max(0.10, (12.40 - strike) * 0.8 + 0.15),
                    "impliedVolatility": 0.35 + (abs(strike - 12.40) / 12.40) * 0.1,
                    "volume": max(10, int(100 - abs(strike - 12.40) * 10)),
                    "openInterest": max(50, int(500 - abs(strike - 12.40) * 50)),
                }
            )

            # Call options
            data.append(
                {
                    "symbol": "TSLL",
                    "optionType": "calls",
                    "strike": strike,
                    "expiration": expiration,
                    "dte": 35,
                    "bid": max(0.05, (strike - 12.40) * 0.8 + 0.1) if strike > 12.40 else max(0.05, 12.40 - strike + 0.1),
                    "ask": max(0.10, (strike - 12.40) * 0.8 + 0.15) if strike > 12.40 else max(0.10, 12.40 - strike + 0.15),
                    "impliedVolatility": 0.35 + (abs(strike - 12.40) / 12.40) * 0.1,
                    "volume": max(10, int(100 - abs(strike - 12.40) * 10)),
                    "openInterest": max(50, int(500 - abs(strike - 12.40) * 50)),
                }
            )

        return pd.DataFrame(data)

    def test_tsll_cash_secured_puts_realistic_scenario(self, comprehensive_options_data):
        """Test TSLL cash-secured puts with realistic market data."""
        analyzer = OptionsStrategiesAnalyzer()
        prices = {"TSLL": 12.40}

        # Use the problematic configuration from the user
        config = {
            "min_annual_roi": 0.1,  # 10%
            "min_dte": 30,
            "max_dte": 45,
            "min_buffer_percent": 0.08,  # 8%
            "max_candidates": 10,
        }

        candidates = analyzer.find_cash_secured_put_candidates(comprehensive_options_data, prices, config)

        # Should find the best candidate (one per symbol as expected)
        assert not candidates.empty
        assert len(candidates) == 1  # Should find exactly one best option per symbol

        # Verify the candidate meets basic requirements
        candidate = candidates.iloc[0]
        assert candidate["optionType"] == "puts"
        assert candidate["strike"] < 12.40  # OTM puts
        assert candidate["dte"] >= 30
        assert candidate["dte"] <= 45

        # With adaptive filtering, should include options with lower buffer for low-priced stocks
        assert candidate["bufferPercent"] >= 0.03  # Adaptive threshold for low-priced stocks

        # Should have calculated all required metrics
        required_fields = ["winRateScore", "annualizedRoi", "bufferPercent", "premium"]
        for field in required_fields:
            assert field in candidates.columns
            assert not pd.isna(candidate[field])

    def test_filtering_impact_analysis_integration(self, comprehensive_options_data):
        """Test the complete filtering impact analysis workflow."""
        analyzer = OptionsStrategiesAnalyzer()
        prices = {"TSLL": 12.40}

        # Use very restrictive parameters to trigger diagnostic analysis
        restrictive_config = {
            "min_annual_roi": 0.50,  # 50% - extremely high to force filtering
            "min_dte": 30,
            "max_dte": 32,  # Very narrow range to force filtering
            "min_buffer_percent": 0.25,  # 25% - extremely high to force filtering
            "max_candidates": 10,
        }

        # Run diagnostic analysis
        diagnostic = analyzer.analyze_filtering_impact(
            comprehensive_options_data, prices, restrictive_config, "cash_secured_puts"
        )

        # Should provide comprehensive analysis
        assert "filter_impact" in diagnostic
        assert "suggestions" in diagnostic
        assert "parameter_recommendations" in diagnostic

        # Should identify the restrictive filters (at least one suggestion should be made)
        # If no suggestions, the test data might not be restrictive enough, which is also valid
        assert isinstance(diagnostic["suggestions"], list)

        # Should provide filter impact analysis
        assert "filter_impact" in diagnostic
        filter_impact = diagnostic["filter_impact"]

        # Should analyze at least the basic filters
        expected_filters = ["option_type", "dte", "otm", "buffer", "roi"]
        for filter_name in expected_filters:
            if filter_name in filter_impact:
                impact = filter_impact[filter_name]
                assert "remaining" in impact
                assert "filtered_out" in impact

    def test_performance_with_large_dataset(self):
        """Test performance with large options dataset."""
        analyzer = OptionsStrategiesAnalyzer()

        # Create large dataset (1000 options)
        large_data = []
        symbols = ["AAPL", "MSFT", "GOOGL", "TSLA", "TSLL"]
        strikes = list(range(50, 250, 5))  # 40 strikes per symbol

        base_date = datetime.now() + timedelta(days=30)
        expiration = base_date.strftime("%Y-%m-%d")

        for symbol in symbols:
            base_price = {"AAPL": 150, "MSFT": 300, "GOOGL": 120, "TSLA": 200, "TSLL": 12.40}[symbol]
            for strike in strikes:
                if strike < base_price * 2:  # Reasonable strike range
                    for option_type in ["puts", "calls"]:
                        large_data.append(
                            {
                                "symbol": symbol,
                                "optionType": option_type,
                                "strike": float(strike),
                                "expiration": expiration,
                                "dte": 35,
                                "bid": max(0.05, abs(base_price - strike) * 0.1),
                                "ask": max(0.10, abs(base_price - strike) * 0.1 + 0.05),
                                "impliedVolatility": 0.25,
                                "volume": 50,
                                "openInterest": 200,
                            }
                        )

        large_df = pd.DataFrame(large_data)
        prices = {"AAPL": 150, "MSFT": 300, "GOOGL": 120, "TSLA": 200, "TSLL": 12.40}
        config = {"min_annual_roi": 0.1, "min_dte": 30, "max_dte": 45, "min_buffer_percent": 0.05, "max_candidates": 20}

        # Should complete within reasonable time
        import time

        start_time = time.time()

        candidates = analyzer.find_cash_secured_put_candidates(large_df, prices, config)

        end_time = time.time()
        processing_time = end_time - start_time

        # Should complete within 5 seconds for 1000+ options
        assert processing_time < 5.0

        # Should find the best candidate per symbol (up to max_candidates symbols)
        if not candidates.empty:
            unique_symbols = candidates["symbol"].nunique()
            assert unique_symbols > 1  # Should analyze multiple symbols
            assert len(candidates) == unique_symbols  # One best candidate per symbol
            assert len(candidates) <= 20  # Respects max_candidates limit


@pytest.mark.unit
@pytest.mark.options
class TestOptionsServiceJSONSerialization:
    """Test JSON serialization fixes for options service."""

    def test_timestamp_conversion_utility(self):
        """Test the convert_timestamps_to_strings utility function."""
        from datetime import datetime

        import numpy as np
        import pandas as pd

        from src.services.options_service import convert_timestamps_to_strings

        # Test data with various timestamp types
        test_data = {
            "pandas_timestamp": pd.Timestamp("2024-01-15 10:30:00"),
            "datetime_obj": datetime(2024, 1, 15, 10, 30, 0),
            "numpy_int": np.int64(42),
            "numpy_float": np.float64(3.14),
            "nan_value": np.nan,
            "nested_dict": {
                "inner_timestamp": pd.Timestamp("2024-02-20"),
                "inner_list": [pd.Timestamp("2024-03-25"), np.int32(100)],
            },
            "list_with_timestamps": [pd.Timestamp("2024-04-30"), datetime(2024, 5, 1), "regular_string"],
        }

        # Convert timestamps
        converted = convert_timestamps_to_strings(test_data)

        # Verify conversions
        assert isinstance(converted["pandas_timestamp"], str)
        assert isinstance(converted["datetime_obj"], str)
        assert isinstance(converted["numpy_int"], int)
        assert isinstance(converted["numpy_float"], float)
        assert converted["nan_value"] is None

        # Verify nested structures
        assert isinstance(converted["nested_dict"]["inner_timestamp"], str)
        assert isinstance(converted["nested_dict"]["inner_list"][0], str)
        assert isinstance(converted["nested_dict"]["inner_list"][1], int)

        # Verify list conversions
        assert isinstance(converted["list_with_timestamps"][0], str)
        assert isinstance(converted["list_with_timestamps"][1], str)
        assert converted["list_with_timestamps"][2] == "regular_string"

    def test_json_serialization_with_candidates(self):
        """Test that candidates with timestamps can be JSON serialized."""
        import json
        from datetime import datetime

        import pandas as pd

        from src.services.options_service import convert_timestamps_to_strings

        # Create realistic candidates data with timestamps
        candidates_data = [
            {
                "symbol": "AAPL",
                "strike": 150.0,
                "expiration": pd.Timestamp("2024-02-16"),
                "dte": 30,
                "bid": 2.50,
                "premium": 250.0,
                "analysis_time": datetime.now(),
                "winRateScore": np.float64(85.5),
            }
        ]

        results_data = {
            "candidates": candidates_data,
            "strategy_type": "cash_secured_puts",
            "analysis_timestamp": datetime.now(),
            "market_conditions": {"vix": np.float32(18.5), "last_updated": pd.Timestamp.now()},
        }

        # Convert and test JSON serialization
        clean_data = convert_timestamps_to_strings(results_data)

        # Should not raise an exception
        json_string = json.dumps(clean_data)

        # Verify it can be loaded back
        loaded_data = json.loads(json_string)

        # Verify structure is preserved
        assert loaded_data["strategy_type"] == "cash_secured_puts"
        assert len(loaded_data["candidates"]) == 1
        assert loaded_data["candidates"][0]["symbol"] == "AAPL"
        assert isinstance(loaded_data["candidates"][0]["expiration"], str)
        assert isinstance(loaded_data["analysis_timestamp"], str)


@pytest.mark.unit
@pytest.mark.options
class TestUnifiedCashCoveredOptions:
    """Test the unified cash-covered options strategy."""

    @pytest.fixture
    def analyzer(self):
        """Create an OptionsStrategiesAnalyzer instance for testing."""
        return OptionsStrategiesAnalyzer()

    @pytest.fixture
    def comprehensive_options_data(self):
        """Create comprehensive options data with both calls and puts."""
        base_date = datetime.now() + timedelta(days=35)
        expiration = base_date.strftime("%Y-%m-%d")

        data = []
        symbols = ["AAPL", "MSFT"]

        for symbol in symbols:
            current_price = {"AAPL": 150.0, "MSFT": 300.0}[symbol]
            strikes = [current_price - 10, current_price - 5, current_price, current_price + 5, current_price + 10]

            for strike in strikes:
                # Put options
                data.append(
                    {
                        "symbol": symbol,
                        "optionType": "puts",
                        "strike": strike,
                        "expiration": expiration,
                        "dte": 35,
                        "bid": max(0.5, abs(current_price - strike) * 0.1),
                        "ask": max(0.6, abs(current_price - strike) * 0.1 + 0.1),
                        "impliedVolatility": 0.25,
                        "volume": 100,
                        "openInterest": 500,
                    }
                )

                # Call options
                data.append(
                    {
                        "symbol": symbol,
                        "optionType": "calls",
                        "strike": strike,
                        "expiration": expiration,
                        "dte": 35,
                        "bid": max(0.5, abs(strike - current_price) * 0.1),
                        "ask": max(0.6, abs(strike - current_price) * 0.1 + 0.1),
                        "impliedVolatility": 0.25,
                        "volume": 100,
                        "openInterest": 500,
                    }
                )

        return pd.DataFrame(data)

    @pytest.fixture
    def sample_prices(self):
        """Sample current prices for testing."""
        return {"AAPL": 150.0, "MSFT": 300.0}

    @pytest.fixture
    def default_params(self):
        """Default parameters for unified strategy testing."""
        return {
            "min_annual_roi": 0.01,  # Very low for testing
            "min_dte": 30,
            "max_dte": 45,
            "min_buffer_percent": 0.01,  # Very low for testing
            "max_candidates": 10,
        }

    def test_unified_strategy_basic_analysis(self, analyzer, comprehensive_options_data, sample_prices, default_params):
        """Test basic unified strategy analysis."""
        results = analyzer.find_cash_covered_options_candidates(comprehensive_options_data, sample_prices, default_params)

        # Verify structure
        assert isinstance(results, dict)
        assert "cash_secured_puts" in results
        assert "covered_calls" in results
        assert "strategy_comparison" in results
        assert "analysis_metadata" in results

        # Verify put results
        puts_results = results["cash_secured_puts"]
        assert "candidates" in puts_results
        assert "count" in puts_results
        assert "summary" in puts_results

        # Verify call results
        calls_results = results["covered_calls"]
        assert "candidates" in calls_results
        assert "count" in calls_results
        assert "summary" in calls_results

        # Verify metadata
        metadata = results["analysis_metadata"]
        assert "total_symbols_analyzed" in metadata
        assert "put_opportunities" in metadata
        assert "call_opportunities" in metadata
        assert metadata["total_symbols_analyzed"] == 2  # AAPL and MSFT

    def test_strategy_comparison_logic(self, analyzer, comprehensive_options_data, sample_prices, default_params):
        """Test the strategy comparison logic."""
        results = analyzer.find_cash_covered_options_candidates(comprehensive_options_data, sample_prices, default_params)

        comparison = results["strategy_comparison"]

        # Should have comparison for each symbol
        assert len(comparison) >= 1

        for symbol, comp in comparison.items():
            assert "symbol" in comp
            assert "current_price" in comp
            assert "put_available" in comp
            assert "call_available" in comp
            assert "recommended_strategy" in comp

            # If both strategies are available, should have metrics for both
            if comp["both_available"]:
                assert "put_metrics" in comp
                assert "call_metrics" in comp
                assert "score_difference" in comp

    def test_empty_data_handling(self, analyzer, sample_prices, default_params):
        """Test handling of empty options data."""
        empty_data = pd.DataFrame()

        results = analyzer.find_cash_covered_options_candidates(empty_data, sample_prices, default_params)

        # Should return proper structure even with empty data
        assert isinstance(results, dict)
        assert "cash_secured_puts" in results
        assert "covered_calls" in results
        assert "strategy_comparison" in results
        assert "analysis_metadata" in results

        # Check the actual structure returned by the method
        assert results["cash_secured_puts"] == []
        assert results["covered_calls"] == []
        assert results["strategy_comparison"] == {}

        # Verify metadata for empty data
        metadata = results["analysis_metadata"]
        assert metadata["total_symbols_analyzed"] == 0
        assert metadata["put_opportunities"] == 0
        assert metadata["call_opportunities"] == 0
        assert metadata["symbols_with_both_strategies"] == 0


@pytest.mark.unit
@pytest.mark.options
class TestDataFrameStructureRecognition:
    """Test DataFrame structure recognition and option type inference."""

    def test_contract_symbol_inference(self):
        """Test option type inference from contract symbols."""
        from src.services.options_data import OptionsDataManager

        manager = OptionsDataManager()

        # Create test DataFrame with contract symbols but no optionType column
        test_data = pd.DataFrame(
            {
                "contractSymbol": [
                    "AAPL240216C00150000",  # Call
                    "AAPL240216P00145000",  # Put
                    "AAPL240216C00155000",  # Call
                    "AAPL240216P00140000",  # Put
                ],
                "strike": [150.0, 145.0, 155.0, 140.0],
                "bid": [2.5, 1.5, 1.0, 0.5],
                "ask": [2.6, 1.6, 1.1, 0.6],
                "impliedVolatility": [0.25, 0.23, 0.27, 0.22],
                "volume": [100, 80, 60, 40],
                "openInterest": [500, 400, 300, 200],
            }
        )

        # Test inference
        result = manager._infer_option_types_from_dataframe(test_data, "AAPL")

        # Should successfully separate calls and puts
        assert "calls" in result
        assert "puts" in result
        assert len(result["calls"]) == 2  # Two calls
        assert len(result["puts"]) == 2  # Two puts

        # Verify option types were added correctly
        for call in result["calls"]:
            assert call["optionType"] == "calls"
            assert "C" in call["contractSymbol"]

        for put in result["puts"]:
            assert put["optionType"] == "puts"
            assert "P" in put["contractSymbol"]

    def test_fallback_inference(self):
        """Test fallback option type inference when contract symbols are unclear."""
        from src.services.options_data import OptionsDataManager

        manager = OptionsDataManager()

        # Create test DataFrame without clear contract symbols
        test_data = pd.DataFrame(
            {
                "strike": [140.0, 145.0, 150.0, 155.0],
                "bid": [2.5, 1.5, 1.0, 0.5],
                "ask": [2.6, 1.6, 1.1, 0.6],
                "impliedVolatility": [0.25, 0.23, 0.27, 0.22],
                "volume": [100, 80, 60, 40],
                "openInterest": [500, 400, 300, 200],
            }
        )

        # Test fallback inference
        result = manager._infer_option_types_from_dataframe(test_data, "AAPL")

        # Should still return structured data
        assert "calls" in result
        assert "puts" in result
        assert len(result["calls"]) + len(result["puts"]) == len(test_data)

        # Should have added optionType to all records
        all_options = result["calls"] + result["puts"]
        for option in all_options:
            assert "optionType" in option
            assert option["optionType"] in ["calls", "puts"]

    def test_empty_dataframe_handling(self):
        """Test handling of empty DataFrame."""
        from src.services.options_data import OptionsDataManager

        manager = OptionsDataManager()

        empty_data = pd.DataFrame()
        result = manager._infer_option_types_from_dataframe(empty_data, "AAPL")

        # Should return empty but structured result
        assert "calls" in result
        assert "puts" in result
        assert result["calls"] == []
        assert result["puts"] == []
