"""
Comprehensive test suite for options data management.

Tests all aspects of options data fetching, caching, and market analysis including:
- Data fetching from external APIs
- Data standardization and validation
- Market conditions analysis
- Caching mechanisms
- Error handling and edge cases
"""

from datetime import datetime, timedelta
from unittest.mock import MagicMock, Mock, patch

import numpy as np
import pandas as pd
import pytest

from src.services.options_data import OptionsDataManager


@pytest.mark.unit
@pytest.mark.options
@pytest.mark.data
@pytest.mark.external
class TestOptionsDataManager:
    """Test the OptionsDataManager functionality."""

    @pytest.fixture
    def data_manager(self):
        """Create an OptionsDataManager instance for testing."""
        return OptionsDataManager()

    @pytest.fixture
    def mock_options_chain_data(self):
        """Create mock options chain data from yahooquery."""
        from datetime import datetime, timedelta

        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

        return {
            "AAPL": {
                "calls": [
                    {
                        "strike": 150.0,
                        "bid": 5.0,
                        "ask": 5.5,
                        "expiration": future_date,
                        "impliedVolatility": 0.25,
                        "volume": 100,
                        "openInterest": 500,
                    },
                    {
                        "strike": 155.0,
                        "bid": 3.0,
                        "ask": 3.5,
                        "expiration": future_date,
                        "impliedVolatility": 0.23,
                        "volume": 75,
                        "openInterest": 300,
                    },
                ],
                "puts": [
                    {
                        "strike": 145.0,
                        "bid": 3.0,
                        "ask": 3.5,
                        "expiration": future_date,
                        "impliedVolatility": 0.28,
                        "volume": 80,
                        "openInterest": 400,
                    },
                    {
                        "strike": 140.0,
                        "bid": 2.0,
                        "ask": 2.5,
                        "expiration": future_date,
                        "impliedVolatility": 0.30,
                        "volume": 60,
                        "openInterest": 250,
                    },
                ],
            }
        }

    @pytest.fixture
    def sample_raw_options_data(self):
        """Create sample raw options data for standardization testing."""
        from datetime import datetime, timedelta

        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

        return pd.DataFrame(
            {
                "strike": [150.0, 155.0, 145.0, 140.0],
                "bid": [5.0, 3.0, 3.0, 2.0],
                "ask": [5.5, 3.5, 3.5, 2.5],
                "expiration": [future_date, future_date, future_date, future_date],
                "impliedVolatility": [0.25, 0.23, 0.28, 0.30],
                "volume": [100, 75, 80, 60],
                "openInterest": [500, 300, 400, 250],
            }
        )

    @patch("src.services.options_data.Ticker")
    def test_fetch_single_symbol_options_success(self, mock_ticker, data_manager, mock_options_chain_data):
        """Test successful fetching of options data for a single symbol."""
        # Mock the Ticker response
        mock_ticker_instance = Mock()
        mock_ticker_instance.option_chain = mock_options_chain_data
        mock_ticker_instance.summary_detail = {"AAPL": {"marketCap": 1000000000}}
        mock_ticker.return_value = mock_ticker_instance

        result = data_manager._fetch_single_symbol_options("AAPL")

        # Should return a DataFrame with options data
        assert not result.empty
        assert len(result) == 4  # 2 calls + 2 puts

        # Should have both calls and puts
        assert "calls" in result["optionType"].values
        assert "puts" in result["optionType"].values

        # Should have symbol column
        assert all(result["symbol"] == "AAPL")

        # Should have required columns
        required_columns = ["symbol", "optionType", "strike", "bid", "ask", "expiration"]
        for col in required_columns:
            assert col in result.columns

    @patch("src.services.options_data.Ticker")
    def test_fetch_single_symbol_options_no_data(self, mock_ticker, data_manager):
        """Test fetching options data when no data is available."""
        # Mock empty response
        mock_ticker_instance = Mock()
        mock_ticker_instance.option_chain = {}
        mock_ticker.return_value = mock_ticker_instance

        result = data_manager._fetch_single_symbol_options("INVALID")

        assert result.empty

    @patch("src.services.options_data.Ticker")
    def test_fetch_single_symbol_options_api_error(self, mock_ticker, data_manager):
        """Test handling of API errors during options data fetching."""
        # Mock API error
        mock_ticker.side_effect = Exception("API Error")

        result = data_manager._fetch_single_symbol_options("AAPL")

        assert result.empty

    def test_standardize_options_data_success(self, data_manager, sample_raw_options_data):
        """Test successful standardization of options data."""
        standardized = data_manager._standardize_options_data(sample_raw_options_data)

        assert not standardized.empty

        # Should have DTE column
        assert "dte" in standardized.columns
        assert all(standardized["dte"] > 0)

        # Should have mid price column
        assert "mid" in standardized.columns
        assert all(standardized["mid"] == (standardized["bid"] + standardized["ask"]) / 2)

        # Should filter out options with zero bid/ask
        assert all(standardized["bid"] > 0)
        assert all(standardized["ask"] > 0)

        # Should filter out options with bid < 0.05
        assert all(standardized["bid"] >= 0.05)

    def test_standardize_options_data_empty(self, data_manager):
        """Test standardization of empty options data."""
        empty_data = pd.DataFrame()

        result = data_manager._standardize_options_data(empty_data)

        assert result.empty

    def test_fetch_current_prices_success(self, data_manager):
        """Test successful fetching of current stock prices."""
        # Mock the stock_cache to return expected prices
        expected_prices = {"AAPL": 152.50, "MSFT": 305.75, "GOOGL": 2485.30}
        data_manager.stock_cache.get_current_prices = Mock(return_value=expected_prices)

        symbols = ["AAPL", "MSFT", "GOOGL"]
        prices = data_manager._fetch_current_prices(symbols)

        assert len(prices) == 3
        assert prices["AAPL"] == 152.50
        assert prices["MSFT"] == 305.75
        assert prices["GOOGL"] == 2485.30

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_detect_volatility_regime_high(self, mock_ticker, data_manager):
        """Test volatility regime detection for high volatility."""
        # Mock high VIX
        mock_vix_instance = Mock()
        high_vix_data = pd.DataFrame({"Close": [35.0, 36.0, 34.5, 35.5, 36.2]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = high_vix_data
        mock_ticker.return_value = mock_vix_instance

        regime = data_manager.detect_volatility_regime(["AAPL"])

        assert regime == "High Volatility"

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_detect_volatility_regime_low(self, mock_ticker, data_manager):
        """Test volatility regime detection for low volatility."""
        # Mock low VIX
        mock_vix_instance = Mock()
        low_vix_data = pd.DataFrame({"Close": [12.0, 11.5, 13.0, 12.5, 11.8]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = low_vix_data
        mock_ticker.return_value = mock_vix_instance

        regime = data_manager.detect_volatility_regime(["AAPL"])

        assert regime == "Low Volatility"

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_detect_volatility_regime_normal(self, mock_ticker, data_manager):
        """Test volatility regime detection for normal volatility."""
        # Mock normal VIX
        mock_vix_instance = Mock()
        normal_vix_data = pd.DataFrame({"Close": [20.0, 21.0, 19.5, 20.5, 21.2]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = normal_vix_data
        mock_ticker.return_value = mock_vix_instance

        regime = data_manager.detect_volatility_regime(["AAPL"])

        assert regime == "Normal"

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_calculate_market_stress_indicator(self, mock_ticker, data_manager):
        """Test market stress indicator calculation."""
        # Mock VIX data
        mock_vix_instance = Mock()
        vix_data = pd.DataFrame({"Close": [25.0, 26.0, 24.5, 25.5, 26.2]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = vix_data
        mock_ticker.return_value = mock_vix_instance

        stress = data_manager.calculate_market_stress_indicator(["AAPL"])

        # Should return a value between 0 and 100
        assert 0 <= stress <= 100
        assert isinstance(stress, float)

        # For VIX around 25, stress should be around 37.5-40.5 range
        # Allow for different calculation methods (last price vs average)
        assert 35.0 <= stress <= 45.0

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_get_market_conditions_comprehensive(self, mock_ticker, data_manager):
        """Test comprehensive market conditions analysis."""
        # Mock VIX data
        mock_vix_instance = Mock()
        vix_data = pd.DataFrame({"Close": [22.0, 23.0, 21.5, 22.5, 23.2]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = vix_data
        mock_ticker.return_value = mock_vix_instance

        conditions = data_manager.get_market_conditions(["AAPL", "MSFT"])

        # Should have all required fields
        assert "volatility_regime" in conditions
        assert "stress_indicator" in conditions
        assert "timestamp" in conditions

        # Values should be reasonable
        assert conditions["volatility_regime"] in ["High Volatility", "Low Volatility", "Normal"]
        assert 0 <= conditions["stress_indicator"] <= 100
        assert isinstance(conditions["timestamp"], str)

        # Timestamp should be recent
        timestamp = datetime.fromisoformat(conditions["timestamp"])
        assert (datetime.now() - timestamp).total_seconds() < 60  # Within last minute
