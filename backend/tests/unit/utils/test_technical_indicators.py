from unittest.mock import MagicMock, patch

import numpy as np
import pandas as pd
import pytest

from src.utils.technical_indicators import (
    calculate_adx,
    calculate_atr,
    calculate_bollinger_bands,
    calculate_historical_volatility,
    calculate_macd,
    calculate_roc,
    calculate_sma,
    calculate_supersmoother,
    calculate_wilder_rsi,
)


class TestTechnicalIndicators:
    """Test suite for technical indicators utility functions."""

    def setup_method(self):
        """Setup test fixtures before each test method."""
        # Create sample price data for testing
        self.sample_prices = pd.Series(
            [
                100,
                102,
                101,
                103,
                105,
                104,
                106,
                108,
                107,
                109,
                111,
                110,
                112,
                114,
                113,
                115,
                117,
                116,
                118,
                120,
                119,
                121,
                123,
                122,
                124,
                126,
                125,
                127,
                129,
                128,
            ],
            name="Close",
        )

        # Create sample OHLC data
        self.sample_high = pd.Series(
            [
                101,
                103,
                102,
                104,
                106,
                105,
                107,
                109,
                108,
                110,
                112,
                111,
                113,
                115,
                114,
                116,
                118,
                117,
                119,
                121,
                120,
                122,
                124,
                123,
                125,
                127,
                126,
                128,
                130,
                129,
            ],
            name="High",
        )

        self.sample_low = pd.Series(
            [
                99,
                101,
                100,
                102,
                104,
                103,
                105,
                107,
                106,
                108,
                110,
                109,
                111,
                113,
                112,
                114,
                116,
                115,
                117,
                119,
                118,
                120,
                122,
                121,
                123,
                125,
                124,
                126,
                128,
                127,
            ],
            name="Low",
        )

    def test_calculate_wilder_rsi_normal_case(self):
        """Test calculate_wilder_rsi with normal price data."""
        rsi = calculate_wilder_rsi(self.sample_prices, period=14)

        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(self.sample_prices)
        assert rsi.index.equals(self.sample_prices.index)

        # RSI should be between 0 and 100
        valid_rsi = rsi.dropna()
        assert all(0 <= val <= 100 for val in valid_rsi)

        # First 14 values should be NaN (period=14)
        assert pd.isna(rsi.iloc[:14]).all()

        # Should have valid values after period
        assert not pd.isna(rsi.iloc[14:]).all()

    def test_calculate_wilder_rsi_empty_series(self):
        """Test calculate_wilder_rsi with empty series."""
        empty_series = pd.Series([], dtype="float64")
        rsi = calculate_wilder_rsi(empty_series)

        assert isinstance(rsi, pd.Series)
        assert len(rsi) == 0
        assert rsi.index.equals(empty_series.index)

    def test_calculate_wilder_rsi_insufficient_data(self):
        """Test calculate_wilder_rsi with insufficient data."""
        short_series = pd.Series([100, 101, 102], name="Close")
        rsi = calculate_wilder_rsi(short_series, period=14)

        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(short_series)
        assert pd.isna(rsi).all()  # All values should be NaN

    def test_calculate_wilder_rsi_custom_period(self):
        """Test calculate_wilder_rsi with custom period."""
        rsi = calculate_wilder_rsi(self.sample_prices, period=5)

        # First 5 values should be NaN
        assert pd.isna(rsi.iloc[:5]).all()

        # Should have valid values after period
        assert not pd.isna(rsi.iloc[5:]).all()

    def test_calculate_macd_normal_case(self):
        """Test calculate_macd with normal price data."""
        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            self.sample_prices, short_window=5, long_window=10, signal_window=3
        )

        assert isinstance(df, pd.DataFrame)
        assert list(df.columns) == ["MACD", "Signal_Line", "MACD_Histogram"]
        assert len(df) == len(self.sample_prices)
        assert df.index.equals(self.sample_prices.index)

        assert isinstance(golden_crosses, list)
        assert isinstance(death_crosses, list)
        assert isinstance(second_golden, list)

        # MACD histogram should be MACD - Signal Line
        valid_rows = df.dropna()
        if not valid_rows.empty:
            np.testing.assert_array_almost_equal(valid_rows["MACD_Histogram"], valid_rows["MACD"] - valid_rows["Signal_Line"])

    def test_calculate_macd_empty_series(self):
        """Test calculate_macd with empty series."""
        empty_series = pd.Series([], dtype="float64")
        df, golden_crosses, death_crosses, second_golden = calculate_macd(empty_series)

        assert isinstance(df, pd.DataFrame)
        assert len(df) == 0
        assert golden_crosses == []
        assert death_crosses == []
        assert second_golden == []

    def test_calculate_macd_insufficient_data(self):
        """Test calculate_macd with insufficient data."""
        short_series = pd.Series([100, 101, 102], name="Close")
        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            short_series, short_window=12, long_window=26, signal_window=9
        )

        assert isinstance(df, pd.DataFrame)
        assert len(df) == len(short_series)
        assert pd.isna(df).all().all()  # All values should be NaN
        assert golden_crosses == []
        assert death_crosses == []
        assert second_golden == []

    def test_calculate_sma_normal_case(self):
        """Test calculate_sma with normal data."""
        sma = calculate_sma(self.sample_prices, window=5)

        assert isinstance(sma, pd.Series)
        assert len(sma) == len(self.sample_prices)
        assert sma.index.equals(self.sample_prices.index)

        # First 4 values should be NaN (window=5)
        assert pd.isna(sma.iloc[:4]).all()

        # Check manual calculation for first valid SMA
        expected_first_sma = self.sample_prices.iloc[:5].mean()
        assert abs(sma.iloc[4] - expected_first_sma) < 1e-10

    def test_calculate_sma_window_larger_than_data(self):
        """Test calculate_sma with window larger than data."""
        short_series = pd.Series([100, 101, 102])
        sma = calculate_sma(short_series, window=10)

        assert isinstance(sma, pd.Series)
        assert pd.isna(sma).all()  # All values should be NaN

    def test_calculate_bollinger_bands_normal_case(self):
        """Test calculate_bollinger_bands with normal data."""
        middle, upper, lower = calculate_bollinger_bands(self.sample_prices, window=10, num_std_dev=2)

        assert isinstance(middle, pd.Series)
        assert isinstance(upper, pd.Series)
        assert isinstance(lower, pd.Series)

        assert len(middle) == len(self.sample_prices)
        assert len(upper) == len(self.sample_prices)
        assert len(lower) == len(self.sample_prices)

        # Middle band should be SMA
        expected_sma = calculate_sma(self.sample_prices, window=10)
        pd.testing.assert_series_equal(middle, expected_sma)

        # Upper band should be above middle, lower band should be below
        valid_data = ~(middle.isna() | upper.isna() | lower.isna())
        if valid_data.any():
            assert (upper[valid_data] >= middle[valid_data]).all()
            assert (lower[valid_data] <= middle[valid_data]).all()

    def test_calculate_bollinger_bands_custom_std_dev(self):
        """Test calculate_bollinger_bands with custom standard deviation."""
        middle, upper, lower = calculate_bollinger_bands(self.sample_prices, window=5, num_std_dev=1.5)

        # Check that bands are closer together with smaller std dev multiplier
        middle2, upper2, lower2 = calculate_bollinger_bands(self.sample_prices, window=5, num_std_dev=2.5)

        valid_data = ~(upper.isna() | upper2.isna())
        if valid_data.any():
            # Bands with smaller multiplier should be closer to middle
            assert ((upper2[valid_data] - middle[valid_data]) > (upper[valid_data] - middle[valid_data])).all()

    def test_calculate_atr_normal_case(self):
        """Test calculate_atr with normal OHLC data."""
        atr = calculate_atr(self.sample_high, self.sample_low, self.sample_prices, window=14)

        assert isinstance(atr, pd.Series)
        assert len(atr) == len(self.sample_prices)

        # ATR should be positive
        valid_atr = atr.dropna()
        assert (valid_atr >= 0).all()

        # First few values might be NaN or valid depending on implementation
        # ATR calculation may not always have NaN at first position
        assert not pd.isna(atr).all()  # Should have some valid values

    def test_calculate_atr_invalid_inputs(self):
        """Test calculate_atr with invalid input types."""
        # Test with non-Series inputs
        atr = calculate_atr([1, 2, 3], [0.5, 1.5, 2.5], [0.8, 1.8, 2.8])

        assert isinstance(atr, pd.Series)
        assert atr.empty or pd.isna(atr).all()

    def test_calculate_atr_mixed_inputs(self):
        """Test calculate_atr with mixed valid/invalid inputs."""
        # One Series, two lists
        atr = calculate_atr(self.sample_high, [1, 2, 3], [0.8, 1.8, 2.8])

        assert isinstance(atr, pd.Series)
        assert atr.index.equals(self.sample_high.index)

    def test_calculate_historical_volatility_normal_case(self):
        """Test calculate_historical_volatility with normal data."""
        volatility = calculate_historical_volatility(self.sample_prices, window=10)

        assert isinstance(volatility, pd.Series)
        assert len(volatility) == len(self.sample_prices)

        # Volatility should be positive
        valid_vol = volatility.dropna()
        assert (valid_vol >= 0).all()

        # First 10 values should be NaN (window=10)
        assert pd.isna(volatility.iloc[:10]).all()

    def test_calculate_historical_volatility_zero_prices(self):
        """Test calculate_historical_volatility with constant prices."""
        constant_prices = pd.Series([100] * 20)
        volatility = calculate_historical_volatility(constant_prices, window=5)

        # Volatility of constant prices should be 0 or NaN
        valid_vol = volatility.dropna()
        if not valid_vol.empty:
            assert (valid_vol == 0).all() or pd.isna(valid_vol).all()

    def test_calculate_roc_normal_case(self):
        """Test calculate_roc with normal data."""
        roc = calculate_roc(self.sample_prices, window=5)

        assert isinstance(roc, pd.Series)
        assert len(roc) == len(self.sample_prices)

        # First 5 values should be NaN (window=5)
        assert pd.isna(roc.iloc[:5]).all()

        # Check manual calculation for first valid ROC
        if not pd.isna(roc.iloc[5]):
            expected_roc = ((self.sample_prices.iloc[5] - self.sample_prices.iloc[0]) / self.sample_prices.iloc[0]) * 100
            assert abs(roc.iloc[5] - expected_roc) < 1e-10

    def test_calculate_roc_zero_base_price(self):
        """Test calculate_roc with zero base price."""
        prices_with_zero = pd.Series([0, 1, 2, 3, 4, 5])
        roc = calculate_roc(prices_with_zero, window=1)

        # Should handle division by zero gracefully
        assert isinstance(roc, pd.Series)
        # First ROC calculation involves division by 0, should be inf or NaN
        assert pd.isna(roc.iloc[1]) or np.isinf(roc.iloc[1])

    @patch("src.utils.technical_indicators.ta.adx")
    def test_calculate_adx_normal_case(self, mock_adx):
        """Test calculate_adx with normal OHLC data."""
        # Mock pandas_ta.adx to return a DataFrame
        mock_df = pd.DataFrame({"ADX_14": [20, 25, 30, 35, 40]}, index=self.sample_prices.index[:5])
        mock_adx.return_value = mock_df

        adx = calculate_adx(self.sample_high[:5], self.sample_low[:5], self.sample_prices[:5])

        assert isinstance(adx, pd.Series)
        assert adx.name == "ADX_14"
        pd.testing.assert_series_equal(adx, mock_df.iloc[:, 0])

        # Verify mock was called with correct parameters
        mock_adx.assert_called_once()
        call_args = mock_adx.call_args
        assert call_args[1]["length"] == 14  # Check keyword argument

    @patch("src.utils.technical_indicators.ta.adx")
    def test_calculate_adx_empty_result(self, mock_adx):
        """Test calculate_adx when pandas_ta returns empty result."""
        mock_adx.return_value = pd.DataFrame()

        adx = calculate_adx(self.sample_high, self.sample_low, self.sample_prices)

        assert isinstance(adx, pd.Series)
        assert adx.name == "ADX_14"
        assert adx.index.equals(self.sample_prices.index)
        assert pd.isna(adx).all()

    @patch("src.utils.technical_indicators.ta.adx")
    def test_calculate_adx_non_dataframe_result(self, mock_adx):
        """Test calculate_adx when pandas_ta returns non-DataFrame."""
        mock_adx.return_value = None

        adx = calculate_adx(self.sample_high, self.sample_low, self.sample_prices)

        assert isinstance(adx, pd.Series)
        assert adx.name == "ADX_14"
        assert adx.index.equals(self.sample_prices.index)
        assert pd.isna(adx).all()

    def test_calculate_supersmoother_normal_case(self):
        """Test calculate_supersmoother with normal data."""
        smoothed = calculate_supersmoother(self.sample_prices, period=10)

        assert isinstance(smoothed, pd.Series)
        assert len(smoothed) == len(self.sample_prices)
        assert smoothed.index.equals(self.sample_prices.index)

        # Should have some valid values
        assert not pd.isna(smoothed).all()

    def test_calculate_supersmoother_with_nans(self):
        """Test calculate_supersmoother with NaN values in input."""
        prices_with_nan = self.sample_prices.copy()
        prices_with_nan.iloc[5:8] = np.nan

        smoothed = calculate_supersmoother(prices_with_nan, period=5)

        assert isinstance(smoothed, pd.Series)
        assert len(smoothed) == len(prices_with_nan)

        # Should handle NaN values gracefully
        assert not pd.isna(smoothed).all()

    def test_calculate_supersmoother_short_series(self):
        """Test calculate_supersmoother with very short series."""
        short_series = pd.Series([100, 101])
        smoothed = calculate_supersmoother(short_series, period=10)

        assert isinstance(smoothed, pd.Series)
        assert len(smoothed) == len(short_series)
        assert smoothed.index.equals(short_series.index)


class TestTechnicalIndicatorsEdgeCases:
    """Test edge cases and missing coverage for technical indicators."""

    def test_calculate_macd_with_datetime_index_second_golden_cross(self):
        """Test MACD second golden cross calculation with DatetimeIndex."""
        # Create data with DatetimeIndex that will generate golden crosses
        dates = pd.date_range("2024-01-01", periods=50, freq="D")

        # Create price data that will generate multiple golden crosses
        prices = pd.Series(
            [
                100,
                99,
                98,
                97,
                96,
                95,
                94,
                93,
                92,
                91,  # Declining trend
                92,
                93,
                94,
                95,
                96,
                97,
                98,
                99,
                100,
                101,  # First golden cross area
                100,
                99,
                98,
                97,
                96,
                95,
                94,
                93,
                92,
                91,  # Death cross area
                92,
                93,
                94,
                95,
                96,
                97,
                98,
                99,
                100,
                101,  # Second golden cross area
                102,
                103,
                104,
                105,
                106,
                107,
                108,
                109,
                110,
                111,  # Continued uptrend
            ],
            index=dates,
        )

        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            prices, short_window=3, long_window=8, signal_window=2
        )

        assert isinstance(df, pd.DataFrame)
        assert isinstance(golden_crosses, list)
        assert isinstance(death_crosses, list)
        assert isinstance(second_golden, list)

        # Should have detected some crosses with DatetimeIndex
        # This tests line 118: days_diff = (current_cross - previous_cross).days
        assert len(golden_crosses) >= 0  # May or may not have crosses depending on data

    def test_calculate_macd_exception_handling_in_second_golden_cross(self):
        """Test MACD exception handling in second golden cross calculation."""
        # Create data with a custom index that might cause KeyError/ValueError
        custom_index = ["a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t"]

        # Create price data that will generate golden crosses
        prices = pd.Series(
            [100, 99, 98, 97, 96, 95, 94, 93, 92, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101], index=custom_index
        )

        # Force a scenario that might trigger the exception handling
        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            prices, short_window=3, long_window=8, signal_window=2
        )

        assert isinstance(df, pd.DataFrame)
        assert isinstance(golden_crosses, list)
        assert isinstance(death_crosses, list)
        assert isinstance(second_golden, list)

        # This tests lines 132-133: except (KeyError, ValueError): continue
        # The function should handle any exceptions gracefully

    def test_calculate_atr_with_nan_high_low_first_element(self):
        """Test ATR calculation with NaN in first high-low element."""
        # Create data where the first high-low calculation results in NaN
        high_prices = pd.Series([np.nan, 102, 103, 104, 105])
        low_prices = pd.Series([np.nan, 100, 101, 102, 103])
        close_prices = pd.Series([100, 101, 102, 103, 104])

        atr = calculate_atr(high_prices, low_prices, close_prices, window=3)

        assert isinstance(atr, pd.Series)
        assert len(atr) == len(close_prices)

        # This tests lines 196-199: handling NaN in first high-low calculation
        # The function should handle NaN values gracefully
        # First TR value should be set to 0 when high-low is NaN
        assert not pd.isna(atr).all()  # Should have some valid values

    def test_calculate_atr_with_valid_high_low_first_element(self):
        """Test ATR calculation with valid high-low in first element but NaN TR."""
        # Create data where TR is NaN but high-low is valid for first element
        high_prices = pd.Series([102, 103, 104, 105, 106])
        low_prices = pd.Series([100, 101, 102, 103, 104])
        close_prices = pd.Series([np.nan, 102, 103, 104, 105])  # First close is NaN

        atr = calculate_atr(high_prices, low_prices, close_prices, window=3)

        assert isinstance(atr, pd.Series)
        assert len(atr) == len(close_prices)

        # This tests line 197: tr.iloc[0] = high_low.iloc[0]
        # When TR is NaN but high-low is valid, should use high-low for first TR
        assert not pd.isna(atr).all()  # Should have some valid values

    def test_calculate_wilder_rsi_edge_cases(self):
        """Test additional edge cases for Wilder RSI calculation."""
        # Test with all identical prices (no change)
        constant_prices = pd.Series([100] * 20)
        rsi = calculate_wilder_rsi(constant_prices, period=14)

        assert isinstance(rsi, pd.Series)
        assert len(rsi) == len(constant_prices)

        # With no price changes, RSI calculation might result in NaN or specific values
        # The function should handle this gracefully

        # Test with extreme price movements
        extreme_prices = pd.Series([100, 200, 50, 300, 25, 400, 10, 500])
        rsi_extreme = calculate_wilder_rsi(extreme_prices, period=5)

        assert isinstance(rsi_extreme, pd.Series)
        valid_rsi = rsi_extreme.dropna()
        if not valid_rsi.empty:
            assert all(0 <= val <= 100 for val in valid_rsi)

    def test_calculate_macd_with_identical_windows(self):
        """Test MACD calculation with identical short and long windows."""
        # Edge case: short_window == long_window
        prices = pd.Series([100, 101, 102, 103, 104, 105, 106, 107, 108, 109])

        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            prices, short_window=5, long_window=5, signal_window=3
        )

        assert isinstance(df, pd.DataFrame)
        # When short_window == long_window, MACD line should be close to 0
        valid_macd = df["MACD"].dropna()
        if not valid_macd.empty:
            assert all(abs(val) < 1e-10 for val in valid_macd)

    def test_calculate_bollinger_bands_edge_cases(self):
        """Test Bollinger Bands with edge cases."""
        # Test with window = 1
        prices = pd.Series([100.0, 101.0, 102.0, 103.0, 104.0])  # Use float to match dtype
        middle, upper, lower = calculate_bollinger_bands(prices, window=1, num_std_dev=2)

        assert isinstance(middle, pd.Series)
        assert isinstance(upper, pd.Series)
        assert isinstance(lower, pd.Series)

        # With window=1, standard deviation should be 0, so upper == middle == lower
        # But rolling with window=1 gives the actual values, not NaN
        valid_data = ~middle.isna()
        if valid_data.any():
            # For window=1, std should be NaN, making upper and lower NaN too
            # But middle should equal the original values
            pd.testing.assert_series_equal(middle, prices, check_dtype=False)

    def test_calculate_macd_keyerror_in_second_golden_cross(self):
        """Test MACD with specific conditions to trigger KeyError in second golden cross."""
        # Create a scenario that might trigger KeyError when getting index location
        prices = pd.Series(
            [
                100,
                99,
                98,
                97,
                96,
                95,
                94,
                93,
                92,
                91,
                92,
                93,
                94,
                95,
                96,
                97,
                98,
                99,
                100,
                101,
                100,
                99,
                98,
                97,
                96,
                95,
                94,
                93,
                92,
                91,
                92,
                93,
                94,
                95,
                96,
                97,
                98,
                99,
                100,
                101,
            ]
        )

        # Use very short windows to force more crosses
        df, golden_crosses, death_crosses, second_golden = calculate_macd(
            prices, short_window=2, long_window=3, signal_window=2
        )

        assert isinstance(df, pd.DataFrame)
        assert isinstance(golden_crosses, list)
        assert isinstance(death_crosses, list)
        assert isinstance(second_golden, list)

        # This should test the exception handling in second golden cross calculation
        # Lines 132-133: except (KeyError, ValueError): continue

    def test_calculate_atr_nan_high_low_edge_case(self):
        """Test ATR with specific NaN pattern to cover line 197."""
        # Create data where high-low is NaN for first element
        high_prices = pd.Series([np.nan, 102, 103, 104, 105])
        low_prices = pd.Series([np.nan, 100, 101, 102, 103])
        close_prices = pd.Series([100, 101, 102, 103, 104])

        atr = calculate_atr(high_prices, low_prices, close_prices, window=3)

        assert isinstance(atr, pd.Series)
        assert len(atr) == len(close_prices)

        # This should test line 197: tr.iloc[0] = 0 when high_low is NaN
        # The function should set TR[0] = 0 when high-low is NaN
        assert not pd.isna(atr).all()  # Should have some valid values

    def test_calculate_macd_force_keyerror_exception(self):
        """Test MACD to force KeyError/ValueError in second golden cross calculation."""
        # Create a custom index that will cause issues in get_loc
        import string

        data_length = 30
        custom_index = [f"idx_{i}" for i in range(data_length)]  # Create 30 unique indices

        # Create oscillating data to generate multiple crosses
        prices = []
        for i in range(data_length):
            if i < 10:
                prices.append(100 - i)  # Declining
            elif i < 15:
                prices.append(90 + (i - 10) * 2)  # Rising (golden cross)
            elif i < 20:
                prices.append(100 - (i - 15))  # Declining (death cross)
            else:
                prices.append(95 + (i - 20) * 2)  # Rising again (second golden cross)

        prices_series = pd.Series(prices, index=custom_index)

        # Patch get_loc to raise KeyError for specific indices
        original_get_loc = prices_series.index.get_loc

        def mock_get_loc(key):
            # Raise KeyError for some specific keys to test exception handling
            if key in ["idx_15", "idx_16", "idx_17"]:  # Some indices that might be golden crosses
                raise KeyError(f"Mock KeyError for {key}")
            return original_get_loc(key)

        # Temporarily replace get_loc method
        prices_series.index.get_loc = mock_get_loc

        try:
            df, golden_crosses, death_crosses, second_golden = calculate_macd(
                prices_series, short_window=3, long_window=6, signal_window=2
            )

            assert isinstance(df, pd.DataFrame)
            assert isinstance(golden_crosses, list)
            assert isinstance(death_crosses, list)
            assert isinstance(second_golden, list)

            # This should test lines 132-133: except (KeyError, ValueError): continue
            # The function should handle the KeyError gracefully

        finally:
            # Restore original get_loc method
            prices_series.index.get_loc = original_get_loc
