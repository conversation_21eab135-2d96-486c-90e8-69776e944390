import io
from datetime import datetime
from unittest.mock import MagicMock, patch

import pandas as pd
import pytest


class TestFileProcessor:
    """Test suite for file_processor utility functions."""

    def setup_method(self):
        """Setup test fixtures before each test method."""
        # Import functions to test
        from src.utils.file_processor import process_row, read_csv_chunks, validate_columns

        self.read_csv_chunks = read_csv_chunks
        self.validate_columns = validate_columns
        self.process_row = process_row

    def test_read_csv_chunks_valid_csv(self):
        """Test read_csv_chunks with valid CSV content."""
        csv_content = "Name,Age,City\nJohn,25,NYC\nJane,30,LA"

        chunks = self.read_csv_chunks(csv_content, chunk_size=1)
        chunk_list = list(chunks)

        assert len(chunk_list) == 2
        assert chunk_list[0].iloc[0]["Name"] == "John"
        assert chunk_list[1].iloc[0]["Name"] == "Jane"

    def test_read_csv_chunks_invalid_csv(self):
        """Test read_csv_chunks with truly invalid CSV content."""
        # Use content that will definitely cause pandas to fail
        invalid_csv = 'Name,Age\n"Unclosed quote'

        with pytest.raises(ValueError, match="CSV 文件格式错误"):
            list(self.read_csv_chunks(invalid_csv))

    def test_read_csv_chunks_empty_content(self):
        """Test read_csv_chunks with empty content."""
        with pytest.raises(ValueError, match="CSV 文件格式错误"):
            list(self.read_csv_chunks(""))

    def test_validate_columns_all_present(self):
        """Test validate_columns when all required columns are present."""
        df = pd.DataFrame({"A": [1, 2], "B": [3, 4], "C": [5, 6]})
        required_columns = ["A", "B"]

        # Should not raise any exception
        self.validate_columns(df, required_columns)

    def test_validate_columns_missing_columns(self):
        """Test validate_columns when required columns are missing."""
        df = pd.DataFrame({"A": [1, 2], "B": [3, 4]})
        required_columns = ["A", "B", "C", "D"]

        with pytest.raises(ValueError, match="缺少必需的列: C, D"):
            self.validate_columns(df, required_columns)

    def test_validate_columns_empty_requirements(self):
        """Test validate_columns with empty required columns list."""
        df = pd.DataFrame({"A": [1, 2]})
        required_columns = []

        # Should not raise any exception
        self.validate_columns(df, required_columns)

    def test_process_row_valid_buy_transaction(self):
        """Test process_row with valid buy transaction."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "100",
                "Price": "150.50",
                "Description": "Buy transaction",
            }
        )
        row.name = 0  # Set row index

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert rsu_vesting is None
        assert rsu_sell is None

        assert transaction["account_id"] == 1
        assert transaction["symbol"] == "AAPL"
        assert transaction["quantity"] == 100.0
        assert transaction["price"] == 150.50
        assert transaction["trans_time"] == "2024-01-15"

    def test_process_row_valid_sell_transaction(self):
        """Test process_row with valid sell transaction."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU SOLD",
                "Symbol": "AAPL",
                "Quantity": "50",
                "Price": "175.25",
                "Description": "Sell transaction",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert rsu_vesting is None
        assert rsu_sell is not None

        assert transaction["quantity"] == -50.0  # Negative for sell
        assert rsu_sell["symbol"] == "AAPL"
        assert rsu_sell["is_rsu_related"] is False

    def test_process_row_rsu_vesting_event(self):
        """Test process_row with RSU vesting event."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "RSU BOUGHT",
                "Symbol": "MSFT",
                "Quantity": "25",
                "Price": "",
                "Description": "RSU vesting",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is None
        assert rsu_vesting is not None
        assert rsu_sell is None

        assert rsu_vesting["symbol"] == "MSFT"
        assert rsu_vesting["quantity"] == 25.0
        assert rsu_vesting["trans_time"] == "2024-01-15"

    def test_process_row_missing_required_field(self):
        """Test process_row with missing required field."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "",  # Missing symbol
                "Quantity": "100",
                "Price": "150.50",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert rsu_vesting is None
        assert rsu_sell is None

        assert error["row"] == 2  # row.name + 2
        assert "无效的股票代码" in error["error"]

    def test_process_row_invalid_date_format(self):
        """Test process_row with invalid date format."""
        row = pd.Series(
            {
                "Run Date": "2024-01-15",  # Wrong format, should be MM/DD/YYYY
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "100",
                "Price": "150.50",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "日期格式错误" in error["error"]

    def test_process_row_expired_transaction(self):
        """Test process_row with expired transaction."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "EXPIRED",
                "Symbol": "AAPL",
                "Quantity": "100",
                "Price": "0.00",  # Provide explicit price for expired
                "Description": "Expired option",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert transaction["price"] == 0.0  # Default price for expired
        assert transaction["quantity"] == -100.0  # Negative for expired

    def test_process_row_reinvestment_transaction(self):
        """Test process_row with reinvestment transaction."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REINVESTMENT",
                "Symbol": "AAPL",
                "Quantity": "10",
                "Price": "150.00",
                "Description": "Dividend reinvestment",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert transaction["quantity"] == 10.0  # Positive for reinvestment (treated as buy)

    def test_process_row_cd_transaction_skipped(self):
        """Test process_row with CD transaction that should be skipped."""
        row = pd.Series(
            {"Run Date": "01/15/2024", "Action": "CD 0% MATURED", "Symbol": "CD001", "Quantity": "1000", "Price": "1.00"}
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        # Should be skipped entirely
        assert transaction is None
        assert error is None
        assert rsu_vesting is None
        assert rsu_sell is None

    def test_process_row_unhandled_action_type(self):
        """Test process_row with unhandled action type."""
        row = pd.Series(
            {"Run Date": "01/15/2024", "Action": "UNKNOWN_ACTION", "Symbol": "AAPL", "Quantity": "100", "Price": "150.50"}
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "无效的交易类型" in error["error"]

    def test_process_row_cash_transaction(self):
        """Test process_row with CASH symbol using default price."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "CASH",
                "Quantity": "1000",
                "Price": "",  # Empty price, should default to 1
                "Description": "Cash transaction",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert transaction["price"] == 1.0  # Default price for CASH
        assert transaction["symbol"] == "CASH"

    def test_process_row_scientific_notation_quantity(self):
        """Test process_row with scientific notation in quantity."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "1.5e2",  # 150 in scientific notation
                "Price": "100.00",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert transaction["quantity"] == 150.0

    def test_process_row_negative_price_error(self):
        """Test process_row with negative price."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "100",
                "Price": "-150.50",  # Negative price
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "无效的价格" in error["error"]

    def test_process_row_calculate_price_from_amount(self):
        """Test process_row calculating price from amount when price is missing."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "100",
                "Price": "",
                "Amount": "15000",  # Should calculate price as 15000/100 = 150
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is not None
        assert error is None
        assert transaction["price"] == 150.0

    def test_process_row_reverse_split_from_record(self):
        """Test process_row with reverse split FROM record."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REVERSE SPLIT R/S FROM 25460E521#",
                "Symbol": "YANG",
                "Quantity": "-500",
                "Price": "0.00",
            }
        )
        row.name = 0

        split_component, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert split_component is not None
        assert error is None
        assert rsu_vesting is None
        assert rsu_sell is None

        assert split_component["type"] == "REVERSE_SPLIT_COMPONENT"
        assert split_component["record_type"] == "FROM"
        assert split_component["ticker"] == "YANG"
        assert split_component["original_id"] == "25460E521"
        assert split_component["new_id"] is None
        assert split_component["quantity"] == -500.0

    def test_process_row_reverse_split_to_record(self):
        """Test process_row with reverse split TO record."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REVERSE SPLIT R/S TO 25461A460# (25460E521) (Cash)",
                "Symbol": "25460E521",
                "Quantity": "25",
                "Price": "0.00",
            }
        )
        row.name = 0

        split_component, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert split_component is not None
        assert error is None
        assert rsu_vesting is None
        assert rsu_sell is None

        assert split_component["type"] == "REVERSE_SPLIT_COMPONENT"
        assert split_component["record_type"] == "TO"
        assert split_component["ticker"] == "25460E521"
        assert split_component["original_id"] == "25460E521"
        assert split_component["new_id"] == "25461A460"
        assert split_component["quantity"] == 25.0

    def test_process_row_reverse_split_invalid_from(self):
        """Test process_row with invalid reverse split FROM record."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REVERSE SPLIT R/S FROM INVALID",  # No valid ID
                "Symbol": "YANG",
                "Quantity": "-500",
                "Price": "0.00",
            }
        )
        row.name = 0

        result = self.process_row(row, account_id=1)

        # Should return all None for invalid records
        assert all(x is None for x in result)

    def test_process_row_reverse_split_invalid_to(self):
        """Test process_row with invalid reverse split TO record."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REVERSE SPLIT R/S TO INVALID",  # No valid IDs
                "Symbol": "YANG",
                "Quantity": "25",
                "Price": "0.00",
            }
        )
        row.name = 0

        result = self.process_row(row, account_id=1)

        # Should return all None for invalid records
        assert all(x is None for x in result)

    def test_process_row_sell_missing_price_error(self):
        """Test process_row with sell transaction missing price."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU SOLD",
                "Symbol": "AAPL",
                "Quantity": "50",
                "Price": "",  # Missing price for sell
                "Description": "Sell without price",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "卖出交易需要价格" in error["error"]

    def test_process_row_invalid_quantity_format(self):
        """Test process_row with invalid quantity format."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "invalid_number",
                "Price": "150.50",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "数量或价格格式错误" in error["error"]

    def test_process_row_invalid_price_format(self):
        """Test process_row with invalid price format."""
        row = pd.Series(
            {"Run Date": "01/15/2024", "Action": "YOU BOUGHT", "Symbol": "AAPL", "Quantity": "100", "Price": "invalid_price"}
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "数量或价格格式错误" in error["error"]

    def test_process_row_rsu_invalid_quantity(self):
        """Test process_row with RSU vesting event with invalid quantity."""
        row = pd.Series(
            {"Run Date": "01/15/2024", "Action": "RSU BOUGHT", "Symbol": "MSFT", "Quantity": "invalid", "Price": ""}
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "RSU数量格式错误" in error["error"]

    def test_process_row_reverse_split_invalid_quantity(self):
        """Test process_row with reverse split having invalid quantity."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "REVERSE SPLIT R/S FROM 25460E521#",
                "Symbol": "YANG",
                "Quantity": "invalid_qty",
                "Price": "0.00",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "Reverse Split数量格式错误" in error["error"]

    def test_process_row_missing_nan_field(self):
        """Test process_row with NaN in required field."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": pd.NA,  # NaN symbol
                "Quantity": "100",
                "Price": "150.50",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "缺少必需字段" in error["error"]

    def test_process_row_calculate_price_zero_division(self):
        """Test process_row with zero quantity causing division error."""
        row = pd.Series(
            {
                "Run Date": "01/15/2024",
                "Action": "YOU BOUGHT",
                "Symbol": "AAPL",
                "Quantity": "0",  # Zero quantity
                "Price": "",
                "Amount": "15000",
            }
        )
        row.name = 0

        transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

        assert transaction is None
        assert error is not None
        assert "缺少价格" in error["error"]  # Actual error message from the code

    def test_process_row_unexpected_exception(self):
        """Test process_row handling unexpected exceptions."""
        row = pd.Series(
            {"Run Date": "01/15/2024", "Action": "YOU BOUGHT", "Symbol": "AAPL", "Quantity": "100", "Price": "150.50"}
        )
        row.name = 0

        # Mock datetime.strptime to raise an unexpected exception
        with patch("src.utils.file_processor.datetime") as mock_datetime:
            mock_datetime.strptime.side_effect = RuntimeError("Unexpected error")

            transaction, error, rsu_vesting, rsu_sell = self.process_row(row, account_id=1)

            assert transaction is None
            assert error is not None
            assert "处理行数据时出错" in error["error"]
