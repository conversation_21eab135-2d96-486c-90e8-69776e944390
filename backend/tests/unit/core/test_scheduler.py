"""
Unit tests for the scheduler module.
"""

import os
import sqlite3
import tempfile
import threading
import time
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, Mock, call, patch

import pandas as pd
import pytest


class TestStockDataManager:
    """Test cases for StockDataManager class."""

    @pytest.fixture(autouse=True)
    def reset_singleton(self):
        """Reset the StockDataManager singleton before each test."""
        from src.core.scheduler import StockDataManager

        StockDataManager._instance = None
        yield
        # Clean up after test
        if StockDataManager._instance:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.execute(
            """
            CREATE TABLE cached_prices (
                symbol TEXT,
                date TEXT,
                close REAL,
                PRIMARY KEY (symbol, date)
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    @pytest.fixture
    def temp_cache_dir(self):
        """Create a temporary cache directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil

        shutil.rmtree(temp_dir, ignore_errors=True)

    def test_stock_data_manager_singleton(self, temp_db, temp_cache_dir):
        """Test that StockDataManager is a singleton."""
        from src.core.scheduler import StockDataManager

        manager1 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        manager2 = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        assert manager1 is manager2

    def test_stock_data_manager_initialization(self, temp_db, temp_cache_dir):
        """Test StockDataManager initialization."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        assert manager.db_path == temp_db
        assert manager.cache_dir == Path(temp_cache_dir)
        assert manager.cache_dir.exists()
        assert manager.stop_flag is not None
        assert manager.initialized is True
        assert manager.logger is not None
        assert manager.model_retrainer is not None

    def test_get_db_connection(self, temp_db, temp_cache_dir):
        """Test database connection."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        assert conn is not None
        assert isinstance(conn, sqlite3.Connection)

        # Test that we can execute queries
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        assert "transactions" in tables
        assert "cached_prices" in tables

        conn.close()

    def test_get_all_tickers(self, temp_db, temp_cache_dir):
        """Test getting all tickers from database."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 50, 155.0, '2023-01-02')"
        )
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()
        tickers = manager.get_all_tickers(conn)
        conn.close()

        assert set(tickers) == {"AAPL", "GOOGL"}

    def test_should_update_cache_no_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file doesn't exist."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Cache file doesn't exist
        should_update = manager.should_update_cache("AAPL")
        assert should_update is True

    def test_should_update_cache_old_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file is old."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create an old cache file
        cache_file = manager.cache_dir / "AAPL_data.parquet"
        cache_file.touch()

        # Set modification time to 2 days ago
        old_time = time.time() - (2 * 24 * 60 * 60)
        os.utime(cache_file, (old_time, old_time))

        should_update = manager.should_update_cache("AAPL")
        assert should_update is True

    def test_should_update_cache_recent_file(self, temp_db, temp_cache_dir):
        """Test should_update_cache when cache file is recent."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create a recent cache file
        cache_file = manager.cache_dir / "AAPL_data.parquet"
        cache_file.touch()

        should_update = manager.should_update_cache("AAPL")
        assert should_update is False

    @patch("src.core.scheduler.yf.Ticker")
    def test_fetch_real_time_price_only_success(self, mock_ticker, temp_db, temp_cache_dir):
        """Test fetching real-time price successfully."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker
        mock_ticker_instance = Mock()
        mock_ticker.return_value = mock_ticker_instance
        mock_ticker_instance.info = {"regularMarketPrice": 150.0}

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        price = manager._fetch_real_time_price_only("AAPL")

        assert price == 150.0
        mock_ticker.assert_called_once_with("AAPL")

    @patch("src.core.scheduler.yf.Ticker")
    def test_fetch_real_time_price_only_failure(self, mock_ticker, temp_db, temp_cache_dir):
        """Test fetching real-time price with failure."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker to raise exception
        mock_ticker.side_effect = Exception("Network error")

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        price = manager._fetch_real_time_price_only("AAPL")

        assert price is None

    @patch("src.core.scheduler.yf.Ticker")
    def test_fetch_yfinance_data_success(self, mock_ticker, temp_db, temp_cache_dir):
        """Test fetching yfinance data successfully."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker
        mock_ticker_instance = Mock()
        mock_ticker.return_value = mock_ticker_instance

        # Mock historical data
        dates = pd.date_range(start="2023-01-01", end="2023-01-10", freq="D")
        mock_history = pd.DataFrame(
            {
                "Open": [150.0] * len(dates),
                "High": [155.0] * len(dates),
                "Low": [145.0] * len(dates),
                "Close": [152.0] * len(dates),
                "Volume": [1000000] * len(dates),
            },
            index=dates,
        )
        mock_ticker_instance.history.return_value = mock_history
        mock_ticker_instance.info = {"regularMarketPrice": 153.0}

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        historical_data, real_time_price = manager._fetch_yfinance_data("AAPL")

        assert historical_data is not None
        assert len(historical_data) == len(dates)
        assert real_time_price == 153.0
        mock_ticker.assert_called_once_with("AAPL")

    @patch("src.core.scheduler.yf.Ticker")
    def test_fetch_yfinance_data_failure(self, mock_ticker, temp_db, temp_cache_dir):
        """Test fetching yfinance data with failure."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker to raise exception
        mock_ticker.side_effect = Exception("API error")

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        historical_data, real_time_price = manager._fetch_yfinance_data("AAPL")

        assert historical_data is None
        assert real_time_price is None

    @patch("src.core.scheduler.random.choice")
    def test_check_random_symbol_price_no_tickers(self, mock_choice, temp_db, temp_cache_dir):
        """Test random symbol price check when no tickers exist."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Should handle empty ticker list gracefully
        manager.check_random_symbol_price()

        # No exception should be raised
        mock_choice.assert_not_called()

    @patch("src.core.scheduler.random.choice")
    def test_check_random_symbol_price_with_data(self, mock_choice, temp_db, temp_cache_dir):
        """Test random symbol price check with existing data."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        today = datetime.now().strftime("%Y-%m-%d")
        conn.execute("INSERT INTO cached_prices (symbol, date, close) VALUES ('AAPL', ?, 150.0)", (today,))
        conn.commit()
        conn.close()

        # Mock random choice and price fetch
        mock_choice.return_value = "AAPL"

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        with (
            patch.object(manager, "_fetch_real_time_price_only", return_value=150.5) as mock_fetch_price,
            patch.object(manager, "update_all_stocks") as mock_update,
        ):
            manager.check_random_symbol_price()

            mock_choice.assert_called_once()
            mock_fetch_price.assert_called_once_with("AAPL")
            # Should trigger full update because no DB price found for today
            mock_update.assert_called_once()

    def test_start_stop_scheduler(self, temp_db, temp_cache_dir):
        """Test starting and stopping the scheduler."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()
        assert manager.scheduler_thread is not None
        assert manager.scheduler_thread.is_alive()

        # Stop scheduler
        manager.stop_scheduler()

        # Give it a moment to stop
        time.sleep(0.1)
        assert manager.stop_flag.is_set()

    @patch("src.core.scheduler.schedule")
    def test_run_scheduler_jobs(self, mock_schedule, temp_db, temp_cache_dir):
        """Test that scheduler sets up jobs correctly."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Mock the schedule module
        mock_schedule.every.return_value.hours.do = Mock()
        mock_schedule.every.return_value.day.at.return_value.do = Mock()
        mock_schedule.run_pending = Mock()

        # Start scheduler in a separate thread and stop it quickly
        manager.start_scheduler()
        time.sleep(0.1)  # Let it set up jobs
        manager.stop_scheduler()

        # Verify jobs were scheduled
        assert mock_schedule.every.called

    def test_run_job_in_thread(self, temp_db, temp_cache_dir):
        """Test running a job in a separate thread."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create a test function that sets a flag
        test_flag = threading.Event()

        def test_job():
            test_flag.set()

        # Run the job
        manager._run_job_in_thread(test_job)

        # Wait for the job to complete
        test_flag.wait(timeout=1.0)
        assert test_flag.is_set()


class TestModelRetrainingScheduler:
    """Test cases for ModelRetrainingScheduler class."""

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    def test_model_retraining_scheduler_initialization(self, temp_db):
        """Test ModelRetrainingScheduler initialization."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)

        assert scheduler.db_path == temp_db
        assert scheduler.logger is not None

    def test_get_db_connection(self, temp_db):
        """Test database connection."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        conn = scheduler.get_db_connection()

        assert conn is not None
        assert isinstance(conn, sqlite3.Connection)
        conn.close()

    def test_get_active_position_tickers_empty(self, temp_db):
        """Test getting active position tickers when none exist."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        tickers = scheduler.get_active_position_tickers()

        assert tickers == []

    def test_get_active_position_tickers_with_data(self, temp_db):
        """Test getting active position tickers with data."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', -50, 155.0, '2023-01-02')"
        )  # Partial sell
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        tickers = scheduler.get_active_position_tickers()

        # Should return tickers with net positive positions
        assert set(tickers) == {"AAPL", "GOOGL"}

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_no_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job when no active tickers."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        scheduler.run_daily_model_retraining_job()

        # Should not call the training pipeline
        mock_pipeline.assert_not_called()

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_with_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job with active tickers."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        scheduler.run_daily_model_retraining_job()

        # Should call the training pipeline with active tickers
        mock_pipeline.assert_called_once_with(ticker_symbols_to_process=["AAPL"], force_retrain=True)

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_with_provided_tickers(self, mock_pipeline, temp_db):
        """Test daily model retraining job with provided ticker list."""
        from src.core.scheduler import ModelRetrainingScheduler

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)
        test_tickers = ["AAPL", "GOOGL", "MSFT"]
        scheduler.run_daily_model_retraining_job(ticker_symbols_to_process=test_tickers)

        # Should call the training pipeline with provided tickers
        mock_pipeline.assert_called_once_with(ticker_symbols_to_process=test_tickers, force_retrain=True)

    @patch("src.core.scheduler.run_training_pipeline_for_tickers")
    def test_run_daily_model_retraining_job_error_handling(self, mock_pipeline, temp_db):
        """Test daily model retraining job error handling."""
        from src.core.scheduler import ModelRetrainingScheduler

        # Mock pipeline to raise exception
        mock_pipeline.side_effect = Exception("Training failed")

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        scheduler = ModelRetrainingScheduler(db_path=temp_db, logger_instance=None)

        # Should not raise exception, just log it
        scheduler.run_daily_model_retraining_job()

        mock_pipeline.assert_called_once()


class TestSchedulerUtilities:
    """Test cases for scheduler utility functions."""

    @patch("src.core.scheduler.get_calendar")
    def test_is_market_open_today_open(self, mock_get_calendar):
        """Test is_market_open_today when market is open."""
        from src.core.scheduler import is_market_open_today

        # Mock calendar
        mock_calendar = Mock()
        mock_get_calendar.return_value = mock_calendar
        mock_calendar.is_session.return_value = True

        result = is_market_open_today()

        assert result is True
        mock_get_calendar.assert_called_once_with("NYSE")
        mock_calendar.is_session.assert_called_once()

    @patch("src.core.scheduler.get_calendar")
    def test_is_market_open_today_closed(self, mock_get_calendar):
        """Test is_market_open_today when market is closed."""
        from src.core.scheduler import is_market_open_today

        # Mock calendar
        mock_calendar = Mock()
        mock_get_calendar.return_value = mock_calendar
        mock_calendar.is_session.return_value = False

        result = is_market_open_today()

        assert result is False

    def test_is_rate_limit_error_yf_error(self):
        """Test is_rate_limit_error with YFRateLimitError."""
        from yfinance.exceptions import YFRateLimitError

        from src.core.scheduler import is_rate_limit_error

        error = YFRateLimitError()
        result = is_rate_limit_error(error)

        assert result is True

    def test_is_rate_limit_error_too_many_requests(self):
        """Test is_rate_limit_error with 'Too Many Requests' message."""
        from src.core.scheduler import is_rate_limit_error

        error = Exception("Too Many Requests")
        result = is_rate_limit_error(error)

        assert result is True

    def test_is_rate_limit_error_other_error(self):
        """Test is_rate_limit_error with other error."""
        from src.core.scheduler import is_rate_limit_error

        error = Exception("Network error")
        result = is_rate_limit_error(error)

        assert result is False

    def test_start_stop_scheduler_functions(self):
        """Test module-level start_scheduler and stop_scheduler functions."""
        from src.core.scheduler import start_scheduler, stock_data_manager, stop_scheduler

        with (
            patch.object(stock_data_manager, "start_scheduler") as mock_start,
            patch.object(stock_data_manager, "stop_scheduler") as mock_stop,
        ):

            start_scheduler()
            mock_start.assert_called_once()

            stop_scheduler()
            mock_stop.assert_called_once()


class TestStockDataManagerAdvanced:
    """Advanced test cases for StockDataManager class."""

    @pytest.fixture(autouse=True)
    def reset_singleton(self):
        """Reset the StockDataManager singleton before each test."""
        from src.core.scheduler import StockDataManager

        if StockDataManager._instance is not None:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None
        yield
        if StockDataManager._instance is not None:
            if hasattr(StockDataManager._instance, "stop_scheduler"):
                StockDataManager._instance.stop_scheduler()
            StockDataManager._instance = None

    @pytest.fixture
    def temp_db(self):
        """Create a temporary database for testing."""
        fd, path = tempfile.mkstemp(suffix=".db")
        os.close(fd)

        # Create basic schema
        conn = sqlite3.connect(path)
        conn.execute(
            """
            CREATE TABLE transactions (
                transaction_id INTEGER PRIMARY KEY,
                account_id INTEGER,
                symbol TEXT,
                quantity REAL,
                price REAL,
                trans_time TEXT
            )
        """
        )
        conn.execute(
            """
            CREATE TABLE cached_prices (
                symbol TEXT,
                date TEXT,
                close REAL,
                PRIMARY KEY (symbol, date)
            )
        """
        )
        conn.commit()
        conn.close()

        yield path
        os.unlink(path)

    @pytest.fixture
    def temp_cache_dir(self):
        """Create a temporary cache directory."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        import shutil

        shutil.rmtree(temp_dir, ignore_errors=True)

    @patch("src.core.scheduler.yf.Ticker")
    def test_update_stock_data_cache_update_needed(self, mock_ticker, temp_db, temp_cache_dir):
        """Test update_stock_data when cache update is needed."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker
        mock_ticker_instance = Mock()
        mock_ticker.return_value = mock_ticker_instance

        # Mock historical data
        dates = pd.date_range(start="2023-01-01", end="2023-01-05", freq="D")
        mock_history = pd.DataFrame(
            {
                "Open": [150.0] * len(dates),
                "High": [155.0] * len(dates),
                "Low": [145.0] * len(dates),
                "Close": [152.0] * len(dates),
                "Volume": [1000000] * len(dates),
            },
            index=dates,
        )
        mock_ticker_instance.history.return_value = mock_history
        mock_ticker_instance.info = {"regularMarketPrice": 153.0}

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        with patch.object(manager, "should_update_cache", return_value=True):
            manager.update_stock_data("AAPL", conn)

        # Verify cache file was created
        cache_file = manager.cache_dir / "AAPL_data.parquet"
        assert cache_file.exists()

        # Verify database was updated
        cursor = conn.execute("SELECT close FROM cached_prices WHERE symbol = 'AAPL'")
        result = cursor.fetchone()
        assert result is not None
        assert result[0] == 153.0

        conn.close()

    @patch("src.core.scheduler.yf.Ticker")
    def test_update_stock_data_cache_recent(self, mock_ticker, temp_db, temp_cache_dir):
        """Test update_stock_data when cache is recent."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker for real-time price only
        mock_ticker_instance = Mock()
        mock_ticker.return_value = mock_ticker_instance
        mock_ticker_instance.info = {"regularMarketPrice": 153.0}

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        with patch.object(manager, "should_update_cache", return_value=False):
            manager.update_stock_data("AAPL", conn)

        # Verify database was updated with real-time price
        cursor = conn.execute("SELECT close FROM cached_prices WHERE symbol = 'AAPL'")
        result = cursor.fetchone()
        assert result is not None
        assert result[0] == 153.0

        conn.close()

    def test_update_stock_data_symbol_cleaning(self, temp_db, temp_cache_dir):
        """Test that update_stock_data cleans symbol names."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        with (
            patch.object(manager, "_fetch_real_time_price_only", return_value=150.0),
            patch.object(manager, "should_update_cache", return_value=False),
        ):

            # Test with symbol that has quotes and whitespace
            manager.update_stock_data(' "AAPL" ', conn)

        # Verify database was updated with cleaned symbol
        cursor = conn.execute("SELECT symbol FROM cached_prices WHERE symbol = 'AAPL'")
        result = cursor.fetchone()
        assert result is not None

        conn.close()

    @patch("src.core.scheduler.yf.Ticker")
    def test_update_stock_data_error_handling(self, mock_ticker, temp_db, temp_cache_dir):
        """Test update_stock_data error handling."""
        from src.core.scheduler import StockDataManager

        # Mock yfinance ticker to raise exception
        mock_ticker.side_effect = Exception("API error")

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)
        conn = manager.get_db_connection()

        # Should not raise exception, just log it
        manager.update_stock_data("AAPL", conn)

        # Verify no data was saved to database
        cursor = conn.execute("SELECT COUNT(*) FROM cached_prices WHERE symbol = 'AAPL'")
        count = cursor.fetchone()[0]
        assert count == 0

        conn.close()

    def test_update_all_stocks(self, temp_db, temp_cache_dir):
        """Test update_all_stocks method."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'GOOGL', 50, 2500.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        with patch.object(manager, "update_stock_data") as mock_update_stock, patch("time.sleep"):  # Speed up the test
            manager.update_all_stocks()

            # Verify update_stock_data was called for each ticker
            assert mock_update_stock.call_count == 2
            called_symbols = [call[0][0] for call in mock_update_stock.call_args_list]
            assert set(called_symbols) == {"AAPL", "GOOGL"}

    @patch("src.core.scheduler.random.choice")
    def test_check_random_symbol_price_triggers_update(self, mock_choice, temp_db, temp_cache_dir):
        """Test that check_random_symbol_price triggers full update when price differs significantly."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        today = datetime.now().strftime("%Y-%m-%d")
        conn.execute("INSERT INTO cached_prices (symbol, date, close) VALUES ('AAPL', ?, 150.0)", (today,))
        conn.commit()
        conn.close()

        # Mock random choice and price fetch with significant difference
        mock_choice.return_value = "AAPL"

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        with (
            patch.object(manager, "_fetch_real_time_price_only", return_value=160.0) as mock_fetch_price,
            patch.object(manager, "update_all_stocks") as mock_update_all,
        ):
            manager.check_random_symbol_price()

            # Should trigger full update due to significant price difference
            mock_update_all.assert_called_once()

    @patch("src.core.scheduler.random.choice")
    def test_check_random_symbol_price_no_db_price(self, mock_choice, temp_db, temp_cache_dir):
        """Test check_random_symbol_price when no database price exists."""
        from src.core.scheduler import StockDataManager

        # Add test data without price data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        # Mock random choice and price fetch
        mock_choice.return_value = "AAPL"

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        with (
            patch.object(manager, "_fetch_real_time_price_only", return_value=150.0) as mock_fetch_price,
            patch.object(manager, "update_all_stocks") as mock_update,
        ):
            manager.check_random_symbol_price()

            # Should trigger full update when no DB price exists
            mock_update.assert_called_once()

    @patch("src.core.scheduler.random.choice")
    def test_check_random_symbol_price_fetch_fails(self, mock_choice, temp_db, temp_cache_dir):
        """Test check_random_symbol_price when real-time price fetch fails."""
        from src.core.scheduler import StockDataManager

        # Add test data
        conn = sqlite3.connect(temp_db)
        conn.execute(
            "INSERT INTO transactions (account_id, symbol, quantity, price, trans_time) VALUES (1, 'AAPL', 100, 150.0, '2023-01-01')"
        )
        conn.commit()
        conn.close()

        # Mock random choice and failed price fetch
        mock_choice.return_value = "AAPL"

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        with (
            patch.object(manager, "_fetch_real_time_price_only", return_value=None) as mock_fetch_price,
            patch.object(manager, "update_all_stocks") as mock_update,
        ):
            manager.check_random_symbol_price()

            # Should not trigger update when fetch fails
            mock_update.assert_not_called()

    def test_scheduler_thread_lifecycle(self, temp_db, temp_cache_dir):
        """Test complete scheduler thread lifecycle."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Initially no scheduler thread
        assert manager.scheduler_thread is None

        # Start scheduler
        manager.start_scheduler()
        assert manager.scheduler_thread is not None
        assert manager.scheduler_thread.is_alive()
        assert not manager.stop_flag.is_set()

        # Stop scheduler
        manager.stop_scheduler()

        # Wait for thread to stop
        manager.scheduler_thread.join(timeout=1.0)
        assert manager.stop_flag.is_set()
        assert not manager.scheduler_thread.is_alive()

    def test_scheduler_already_running(self, temp_db, temp_cache_dir):
        """Test starting scheduler when already running."""
        from src.core.scheduler import StockDataManager

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()
        first_thread = manager.scheduler_thread

        # Try to start again
        manager.start_scheduler()

        # Should be the same thread
        assert manager.scheduler_thread is first_thread

        # Clean up
        manager.stop_scheduler()
        manager.scheduler_thread.join(timeout=1.0)

    @patch("src.core.scheduler.schedule.run_pending")
    def test_scheduler_error_handling(self, mock_run_pending, temp_db, temp_cache_dir):
        """Test scheduler error handling during execution."""
        from src.core.scheduler import StockDataManager

        # Mock schedule.run_pending to raise exception
        mock_run_pending.side_effect = Exception("Scheduler error")

        manager = StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Start scheduler
        manager.start_scheduler()

        # Let it run briefly to encounter the error
        time.sleep(0.2)

        # Scheduler should still be running (error handling should keep it alive)
        assert manager.scheduler_thread.is_alive()

        # Stop scheduler
        manager.stop_scheduler()
        manager.scheduler_thread.join(timeout=5.0)

        # Now it should have stopped
        assert not manager.scheduler_thread.is_alive()

    def test_threading_safety(self, temp_db, temp_cache_dir):
        """Test thread safety of singleton pattern."""
        import concurrent.futures

        from src.core.scheduler import StockDataManager

        instances = []

        def create_instance():
            return StockDataManager(db_path=temp_db, cache_dir=temp_cache_dir)

        # Create multiple instances concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(create_instance) for _ in range(10)]
            instances = [future.result() for future in futures]

        # All instances should be the same object
        first_instance = instances[0]
        for instance in instances[1:]:
            assert instance is first_instance
