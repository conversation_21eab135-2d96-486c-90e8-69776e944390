"""
Comprehensive test suite for options analysis integration.

Tests all aspects of the integrated options analysis functionality including:
- Database schema and migrations
- Options analysis services
- API endpoints
- Data fetching and caching
- Strategy analysis algorithms
"""

import json
import os
import sqlite3
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, Mock, call, patch

import numpy as np
import pandas as pd
import pytest

from src.api import get_db_connection
from src.app import app as flask_app
from src.services.options_data import OptionsDataManager
from src.services.options_service import OptionsAnalysisService
from src.services.options_strategies import OptionsStrategiesAnalyzer

# Import test helpers
from tests.utils.test_helpers import APITestHelper, DatabaseTestHelper

# Use fixtures from main conftest.py


@pytest.mark.integration
@pytest.mark.options
@pytest.mark.database
class TestOptionsService:
    """Test the OptionsAnalysisService functionality."""

    @pytest.fixture
    def options_service(self, test_db):
        """Create an OptionsAnalysisService instance for testing."""
        return OptionsAnalysisService()

    @pytest.fixture
    def sample_account_id(self, test_db):
        """Create a test account and return its ID."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_data = DatabaseTestHelper.create_test_account(conn)
        conn.close()
        return account_data["account_id"]

    @pytest.fixture
    def multiple_accounts(self, test_db):
        """Create multiple test accounts for isolation testing."""
        conn = DatabaseTestHelper.get_connection(test_db)
        account_ids = []

        for i in range(3):
            account_data = DatabaseTestHelper.create_test_account(conn, account_name=f"Test Account {i + 1}")
            account_ids.append(account_data["account_id"])

        conn.close()
        return account_ids

    def test_create_watchlist(self, options_service, sample_account_id):
        """Test creating a new watchlist with various symbol combinations."""
        symbols = ["AAPL", "MSFT", "GOOGL"]
        name = "Tech Stocks"

        watchlist_id = options_service.create_or_update_watchlist(sample_account_id, symbols, name)

        assert watchlist_id is not None
        assert isinstance(watchlist_id, int)
        assert watchlist_id > 0

        # Verify the watchlist was created
        watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        assert watchlist is not None
        assert watchlist["name"] == name
        assert watchlist["symbols"] == symbols
        assert "created_at" in watchlist
        assert "updated_at" in watchlist

    def test_create_watchlist_with_empty_symbols(self, options_service, sample_account_id):
        """Test creating a watchlist with empty symbols list should still work."""
        symbols = []
        name = "Empty Watchlist"

        watchlist_id = options_service.create_or_update_watchlist(sample_account_id, symbols, name)

        assert watchlist_id is not None
        watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        assert watchlist["symbols"] == []

    def test_create_watchlist_with_duplicate_symbols(self, options_service, sample_account_id):
        """Test creating a watchlist with duplicate symbols."""
        symbols = ["AAPL", "MSFT", "AAPL", "GOOGL", "MSFT"]
        name = "Duplicate Test"

        watchlist_id = options_service.create_or_update_watchlist(sample_account_id, symbols, name)

        watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        # Should preserve duplicates as user might want them for some reason
        assert len(watchlist["symbols"]) == 5

    def test_create_watchlist_with_long_name(self, options_service, sample_account_id):
        """Test creating a watchlist with a very long name."""
        symbols = ["AAPL"]
        name = "A" * 200  # Very long name

        watchlist_id = options_service.create_or_update_watchlist(sample_account_id, symbols, name)

        watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        assert watchlist["name"] == name

    def test_update_watchlist(self, options_service, sample_account_id):
        """Test updating an existing watchlist."""
        # Create initial watchlist
        initial_symbols = ["AAPL", "MSFT"]
        initial_name = "Initial List"
        watchlist_id = options_service.create_or_update_watchlist(sample_account_id, initial_symbols, initial_name)

        # Get initial timestamps
        initial_watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        initial_created_at = initial_watchlist["created_at"]

        # Update the watchlist
        updated_symbols = ["AAPL", "MSFT", "GOOGL", "AMZN"]
        updated_name = "Updated Tech List"

        result_id = options_service.create_or_update_watchlist(sample_account_id, updated_symbols, updated_name, watchlist_id)

        assert result_id == watchlist_id

        # Verify the update
        watchlist = options_service.get_user_watchlist(sample_account_id, watchlist_id)
        assert watchlist["name"] == updated_name
        assert watchlist["symbols"] == updated_symbols
        assert watchlist["created_at"] == initial_created_at  # Should not change
        assert watchlist["updated_at"] != initial_watchlist["updated_at"]  # Should change

    def test_update_nonexistent_watchlist(self, options_service, sample_account_id):
        """Test updating a watchlist that doesn't exist should create a new one."""
        nonexistent_id = 99999
        symbols = ["AAPL", "MSFT"]
        name = "New List"

        # This should create a new watchlist, not update the nonexistent one
        result_id = options_service.create_or_update_watchlist(sample_account_id, symbols, name, nonexistent_id)

        # Should get a new ID, not the nonexistent one
        assert result_id != nonexistent_id

        # Verify the new watchlist exists
        watchlist = options_service.get_user_watchlist(sample_account_id, result_id)
        assert watchlist is not None
        assert watchlist["name"] == name
        assert watchlist["symbols"] == symbols

    def test_get_all_watchlists(self, options_service, sample_account_id):
        """Test retrieving all watchlists for an account."""
        # Create multiple watchlists
        watchlist1_id = options_service.create_or_update_watchlist(sample_account_id, ["AAPL", "MSFT"], "Tech Stocks")
        watchlist2_id = options_service.create_or_update_watchlist(sample_account_id, ["JPM", "BAC"], "Bank Stocks")
        watchlist3_id = options_service.create_or_update_watchlist(sample_account_id, ["XOM", "CVX"], "Energy Stocks")

        # Get all watchlists
        watchlists = options_service.get_user_watchlist(sample_account_id)

        assert len(watchlists) == 3
        watchlist_ids = [w["watchlist_id"] for w in watchlists]
        assert watchlist1_id in watchlist_ids
        assert watchlist2_id in watchlist_ids
        assert watchlist3_id in watchlist_ids

        # Verify each watchlist has the correct structure
        for watchlist in watchlists:
            assert "watchlist_id" in watchlist
            assert "name" in watchlist
            assert "symbols" in watchlist
            assert "created_at" in watchlist
            assert "updated_at" in watchlist
            assert isinstance(watchlist["symbols"], list)

    def test_watchlist_account_isolation(self, options_service, multiple_accounts):
        """Test that watchlists are properly isolated between accounts."""
        account1, account2, account3 = multiple_accounts

        # Create watchlists for different accounts
        watchlist1_id = options_service.create_or_update_watchlist(account1, ["AAPL", "MSFT"], "Account 1 Tech")
        watchlist2_id = options_service.create_or_update_watchlist(account2, ["JPM", "BAC"], "Account 2 Banks")
        watchlist3_id = options_service.create_or_update_watchlist(account1, ["GOOGL", "AMZN"], "Account 1 More Tech")

        # Account 1 should see 2 watchlists
        account1_watchlists = options_service.get_user_watchlist(account1)
        assert len(account1_watchlists) == 2
        account1_ids = [w["watchlist_id"] for w in account1_watchlists]
        assert watchlist1_id in account1_ids
        assert watchlist3_id in account1_ids
        assert watchlist2_id not in account1_ids

        # Account 2 should see 1 watchlist
        account2_watchlists = options_service.get_user_watchlist(account2)
        assert len(account2_watchlists) == 1
        assert account2_watchlists[0]["watchlist_id"] == watchlist2_id

        # Account 3 should see 0 watchlists
        account3_watchlists = options_service.get_user_watchlist(account3)
        assert len(account3_watchlists) == 0

    def test_get_nonexistent_watchlist(self, options_service, sample_account_id):
        """Test retrieving a watchlist that doesn't exist."""
        nonexistent_id = 99999
        result = options_service.get_user_watchlist(sample_account_id, nonexistent_id)
        assert result is None

    def test_get_watchlist_wrong_account(self, options_service, multiple_accounts):
        """Test retrieving a watchlist with wrong account ID."""
        account1, account2, _ = multiple_accounts

        # Create watchlist for account1
        watchlist_id = options_service.create_or_update_watchlist(account1, ["AAPL"], "Account 1 List")

        # Try to retrieve with account2 - should return None
        result = options_service.get_user_watchlist(account2, watchlist_id)
        assert result is None

    def test_strategy_config_management(self, options_service, sample_account_id, strategy_configs):
        """Test strategy configuration save and retrieval."""
        strategy_type = "cash_secured_puts"
        config_data = strategy_configs[strategy_type].copy()
        config_data["min_dte"] = 25  # Modify from default
        config_data["max_dte"] = 50

        # Save configuration
        config_id = options_service.save_strategy_config(sample_account_id, strategy_type, config_data)

        assert config_id is not None
        assert isinstance(config_id, int)
        assert config_id > 0

        # Retrieve configuration
        retrieved_config = options_service.get_strategy_config(sample_account_id, strategy_type)

        assert retrieved_config == config_data

    def test_strategy_config_update(self, options_service, sample_account_id):
        """Test updating an existing strategy configuration."""
        strategy_type = "covered_calls"

        # Save initial configuration
        initial_config = {"min_dte": 20, "max_dte": 45, "min_annual_roi": 0.12}

        config_id1 = options_service.save_strategy_config(sample_account_id, strategy_type, initial_config)

        # Update configuration
        updated_config = {"min_dte": 25, "max_dte": 60, "min_annual_roi": 0.15, "max_delta": 0.25}

        config_id2 = options_service.save_strategy_config(sample_account_id, strategy_type, updated_config)

        # Should get a new config ID
        assert config_id2 != config_id1

        # Retrieved config should be the updated one
        retrieved_config = options_service.get_strategy_config(sample_account_id, strategy_type)

        assert retrieved_config == updated_config

    def test_strategy_config_default_flag(self, options_service, sample_account_id, monkeypatch):
        """Test saving configuration with default flag."""
        # Patch the service's get_db_connection to use the test database
        from src.services import options_service as options_service_module

        monkeypatch.setattr(options_service_module, "get_db_connection", get_db_connection)

        strategy_type = "iron_condors"
        config_data = {"min_dte": 35, "max_dte": 55, "min_annual_roi": 0.25}

        # Save as default configuration
        config_id = options_service.save_strategy_config(sample_account_id, strategy_type, config_data, is_default=True)

        assert config_id is not None

        # Verify it's saved as default
        conn = get_db_connection()
        result = conn.execute(
            """
            SELECT is_default FROM strategy_configs WHERE config_id = ?
        """,
            (config_id,),
        ).fetchone()
        conn.close()

        # Check that the is_default flag is truthy (could be 1 or True)
        assert result is not None, "Config not found in database"
        assert result[0], f"Expected is_default to be truthy, got {result[0]}"

    def test_strategy_config_account_isolation(self, options_service, multiple_accounts):
        """Test that strategy configurations are isolated between accounts."""
        account1, account2, _ = multiple_accounts
        strategy_type = "cash_secured_puts"

        # Save different configs for different accounts
        config1 = {"min_dte": 20, "max_dte": 40}
        config2 = {"min_dte": 30, "max_dte": 60}

        options_service.save_strategy_config(account1, strategy_type, config1)
        options_service.save_strategy_config(account2, strategy_type, config2)

        # Each account should get their own config
        retrieved_config1 = options_service.get_strategy_config(account1, strategy_type)
        retrieved_config2 = options_service.get_strategy_config(account2, strategy_type)

        assert retrieved_config1 == config1
        assert retrieved_config2 == config2
        assert retrieved_config1 != retrieved_config2

    def test_default_strategy_configs(self, options_service):
        """Test default strategy configuration generation for all strategy types."""
        strategies = ["cash_secured_puts", "covered_calls", "iron_condors"]

        for strategy in strategies:
            config = options_service._get_default_strategy_config(strategy)
            assert isinstance(config, dict)
            assert len(config) > 0

            # All strategies have flat configs
            assert "min_dte" in config
            assert "max_dte" in config
            assert "min_annual_roi" in config
            assert config["min_dte"] > 0
            assert config["max_dte"] > config["min_dte"]
            assert 0 < config["min_annual_roi"] < 1

    def test_get_strategy_config_fallback_to_default(self, options_service, sample_account_id):
        """Test that get_strategy_config falls back to default when no custom config exists."""
        strategy_type = "cash_secured_puts"

        # Don't save any custom config, should get default
        retrieved_config = options_service.get_strategy_config(sample_account_id, strategy_type)

        default_config = options_service._get_default_strategy_config(strategy_type)
        assert retrieved_config == default_config

    def test_invalid_strategy_type_default_config(self, options_service):
        """Test default config for invalid strategy type."""
        invalid_strategy = "invalid_strategy_type"
        config = options_service._get_default_strategy_config(invalid_strategy)
        assert config == {}  # Should return empty dict for invalid strategy


class TestOptionsStrategies:
    """Test the OptionsStrategiesAnalyzer functionality."""

    @pytest.fixture
    def analyzer(self):
        """Create an OptionsStrategiesAnalyzer instance for testing."""
        return OptionsStrategiesAnalyzer()

    @pytest.fixture
    def sample_options_data(self):
        """Create sample options data for testing."""
        return pd.DataFrame(
            {
                "symbol": ["AAPL"] * 8,
                "optionType": ["puts", "puts", "puts", "puts", "calls", "calls", "calls", "calls"],
                "strike": [140, 145, 150, 155, 160, 165, 170, 175],
                "expiration": ["2024-02-16"] * 8,
                "dte": [30] * 8,
                "bid": [2.0, 3.5, 5.0, 7.5, 4.0, 2.5, 1.5, 0.8],
                "ask": [2.2, 3.7, 5.2, 7.7, 4.2, 2.7, 1.7, 1.0],
                "impliedVolatility": [0.25, 0.26, 0.27, 0.28, 0.24, 0.23, 0.22, 0.21],
                "volume": [100, 150, 200, 120, 180, 90, 60, 30],
                "openInterest": [500, 600, 800, 400, 700, 300, 200, 100],
            }
        )

    @pytest.fixture
    def sample_prices(self):
        """Create sample current prices."""
        return {"AAPL": 152.50}

    def test_cash_secured_puts_analysis(self, analyzer, sample_options_data, sample_prices):
        """Test cash-secured puts candidate analysis."""
        params = {"min_dte": 20, "max_dte": 60, "min_annual_roi": 0.10, "min_buffer_percent": 0.02, "max_candidates": 5}

        candidates = analyzer.find_cash_secured_put_candidates(sample_options_data, sample_prices, params)

        assert not candidates.empty
        assert all(candidates["optionType"] == "puts")  # Should only contain puts
        assert all(candidates["strike"] < sample_prices["AAPL"])  # OTM puts only
        assert all(candidates["dte"] >= params["min_dte"])
        assert all(candidates["dte"] <= params["max_dte"])
        assert "winRateScore" in candidates.columns
        assert "annualizedRoi" in candidates.columns
        assert "bufferPercent" in candidates.columns

    def test_covered_calls_analysis(self, analyzer, sample_options_data, sample_prices):
        """Test covered calls candidate analysis."""
        params = {"min_dte": 20, "max_dte": 60, "min_annual_roi": 0.08, "min_upside_buffer": 0.02, "max_candidates": 5}

        candidates = analyzer.find_covered_call_candidates(sample_options_data, sample_prices, params)

        assert not candidates.empty
        assert all(candidates["optionType"] == "calls")  # Should only contain calls
        assert all(candidates["strike"] >= sample_prices["AAPL"])  # ATM/OTM calls only
        assert all(candidates["dte"] >= params["min_dte"])
        assert all(candidates["dte"] <= params["max_dte"])
        assert "winRateScore" in candidates.columns
        assert "annualizedRoi" in candidates.columns
        assert "upsideBufferPercent" in candidates.columns

    def test_iron_condor_analysis(self, analyzer, sample_options_data, sample_prices):
        """Test iron condor candidate analysis."""
        # Expand sample data for iron condor testing
        expanded_data = sample_options_data.copy()
        # Add more strikes for iron condor construction
        additional_data = pd.DataFrame(
            {
                "symbol": ["AAPL"] * 8,
                "optionType": ["puts", "puts", "puts", "puts", "calls", "calls", "calls", "calls"],
                "strike": [130, 135, 180, 185, 185, 190, 195, 200],
                "expiration": ["2024-02-16"] * 8,
                "dte": [30] * 8,
                "bid": [0.5, 1.0, 0.3, 0.1, 0.2, 0.1, 0.05, 0.02],
                "ask": [0.7, 1.2, 0.5, 0.3, 0.4, 0.3, 0.25, 0.22],
                "impliedVolatility": [0.30, 0.29, 0.20, 0.19, 0.19, 0.18, 0.17, 0.16],
                "volume": [50, 80, 20, 10, 15, 8, 5, 2],
                "openInterest": [200, 300, 100, 50, 80, 40, 20, 10],
            }
        )
        expanded_data = pd.concat([expanded_data, additional_data], ignore_index=True)

        params = {
            "min_dte": 20,
            "max_dte": 60,
            "min_annual_roi": 0.15,
            "target_delta_short_put": 0.20,
            "target_delta_short_call": 0.20,
            "min_wing_width": 5,
            "max_wing_width": 20,
            "max_candidates": 3,
        }

        candidates = analyzer.find_iron_condor_candidates(expanded_data, sample_prices, params)

        # Iron condors might not be found with limited test data, but test structure
        assert isinstance(candidates, pd.DataFrame)
        if not candidates.empty:
            assert "netPremium" in candidates.columns
            assert "maxProfit" in candidates.columns
            assert "maxLoss" in candidates.columns
            assert "probabilityOfProfit" in candidates.columns
            assert "winRateScore" in candidates.columns

    def test_win_rate_score_calculation(self, analyzer, sample_options_data, sample_prices):
        """Test win rate score calculation for puts."""
        # Add required columns for win rate calculation
        test_data = sample_options_data[sample_options_data["optionType"] == "puts"].copy()
        test_data["currentPrice"] = sample_prices["AAPL"]
        test_data["bufferPercent"] = (test_data["currentPrice"] - test_data["strike"]) / test_data["currentPrice"]

        win_rate_scores = analyzer._calculate_put_win_rate_score(test_data)

        assert len(win_rate_scores) == len(test_data)
        assert all(win_rate_scores >= 0)
        assert all(win_rate_scores <= 100)
        assert isinstance(win_rate_scores, pd.Series)

    def test_dte_categorization(self, analyzer):
        """Test DTE categorization logic."""
        test_cases = [(15, "Short"), (25, "Optimal"), (35, "Optimal"), (45, "Extended"), (65, "Long")]

        for dte, expected_category in test_cases:
            category = analyzer._categorize_dte(dte)
            assert category == expected_category


class TestOptionsDataManager:
    """Test the OptionsDataManager functionality."""

    @pytest.fixture
    def data_manager(self):
        """Create an OptionsDataManager instance for testing."""
        return OptionsDataManager()

    @patch("src.utils.stock_cache.yf.Ticker")
    def test_market_conditions_analysis(self, mock_ticker, data_manager):
        """Test market conditions analysis."""
        # Mock VIX data
        mock_vix_instance = Mock()
        mock_vix_data = pd.DataFrame({"Close": [25.5, 26.0, 24.8, 25.2, 25.8]}, index=pd.date_range("2024-01-01", periods=5))
        mock_vix_instance.history.return_value = mock_vix_data
        mock_ticker.return_value = mock_vix_instance

        symbols = ["AAPL", "MSFT"]
        conditions = data_manager.get_market_conditions(symbols)

        assert "volatility_regime" in conditions
        assert "stress_indicator" in conditions
        assert "timestamp" in conditions
        assert conditions["volatility_regime"] in ["High Volatility", "Low Volatility", "Normal"]
        assert 0 <= conditions["stress_indicator"] <= 100

    def test_standardize_options_data(self, data_manager):
        """Test options data standardization."""
        # Create sample raw options data with future expiration
        from datetime import datetime, timedelta

        future_date = (datetime.now() + timedelta(days=30)).strftime("%Y-%m-%d")

        raw_data = pd.DataFrame(
            {
                "strike": [150.0, 155.0, 160.0],
                "bid": [5.0, 3.0, 1.5],
                "ask": [5.5, 3.5, 2.0],
                "expiration": [future_date, future_date, future_date],
                "impliedVolatility": [0.25, 0.28, 0.30],
                "volume": [100, 50, 25],
                "openInterest": [500, 300, 150],
            }
        )

        standardized = data_manager._standardize_options_data(raw_data)

        assert not standardized.empty
        assert "dte" in standardized.columns
        assert "mid" in standardized.columns
        assert all(standardized["bid"] > 0)
        assert all(standardized["ask"] > 0)
        assert all(standardized["dte"] > 0)

    def test_current_prices_fetching(self, data_manager):
        """Test current price fetching."""
        # Mock the stock_cache to return expected prices
        expected_prices = {"AAPL": 152.50, "MSFT": 305.75}
        data_manager.stock_cache.get_current_prices = Mock(return_value=expected_prices)

        symbols = ["AAPL", "MSFT"]
        prices = data_manager._fetch_current_prices(symbols)

        assert "AAPL" in prices
        assert "MSFT" in prices
        assert prices["AAPL"] == 152.50
        assert prices["MSFT"] == 305.75


class TestOptionsAPI:
    """Test the options analysis API endpoints."""

    @pytest.fixture
    def client(self, app):
        """Create a test client for the Flask app."""
        return app.test_client()

    @pytest.fixture
    def sample_account(self, test_db):
        """Create a test account."""
        conn = get_db_connection()
        cursor = conn.execute("INSERT INTO accounts (name) VALUES (?)", ("Test Account",))
        conn.commit()
        account_id = cursor.lastrowid
        conn.close()
        return account_id

    def test_get_watchlists_endpoint(self, client, sample_account):
        """Test the GET watchlists endpoint."""
        response = client.get(f"/api/strategies/options/watchlists?account_id={sample_account}")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert "watchlists" in data
        assert data["status"] == "success"

    def test_create_watchlist_endpoint(self, client, sample_account):
        """Test the POST watchlists endpoint."""
        watchlist_data = {"account_id": sample_account, "name": "Test Watchlist", "symbols": ["AAPL", "MSFT", "GOOGL"]}

        response = client.post(
            "/api/strategies/options/watchlists", data=json.dumps(watchlist_data), content_type="application/json"
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert "watchlist_id" in data
        assert data["status"] == "success"

    def test_get_strategy_config_endpoint(self, client, sample_account):
        """Test the GET strategy config endpoint."""
        strategy_type = "cash_secured_puts"
        response = client.get(f"/api/strategies/options/config/{strategy_type}?account_id={sample_account}")

        assert response.status_code == 200
        data = json.loads(response.data)
        assert "config" in data
        assert data["strategy_type"] == strategy_type
        assert data["status"] == "success"

    def test_save_strategy_config_endpoint(self, client, sample_account):
        """Test the POST strategy config endpoint."""
        strategy_type = "cash_secured_puts"
        config_data = {"account_id": sample_account, "config": {"min_dte": 25, "max_dte": 50, "min_annual_roi": 0.20}}

        response = client.post(
            f"/api/strategies/options/config/{strategy_type}", data=json.dumps(config_data), content_type="application/json"
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert "config_id" in data
        assert data["status"] == "success"

    @patch("src.services.options_strategies.OptionsStrategiesAnalyzer.find_iron_condor_candidates")
    @patch("src.services.options_data.OptionsDataManager.fetch_data_for_batch")
    @patch("src.services.options_data.OptionsDataManager.get_market_conditions")
    def test_analyze_options_endpoint(self, mock_market_conditions, mock_fetch_data, mock_iron_condor, client, sample_account):
        """Test the options analysis endpoint."""
        # Mock data fetching
        mock_options_df = pd.DataFrame(
            {
                "symbol": ["AAPL", "AAPL"],
                "optionType": ["puts", "calls"],
                "strike": [145.0, 160.0],
                "expiration": ["2024-02-16", "2024-02-16"],
                "dte": [30, 30],
                "bid": [3.0, 2.0],
                "ask": [3.5, 2.5],
                "impliedVolatility": [0.25, 0.23],
            }
        )
        mock_prices = {"AAPL": 152.50}
        mock_fetch_data.return_value = (mock_options_df, mock_prices)

        mock_market_conditions.return_value = {
            "volatility_regime": "Normal",
            "stress_indicator": 45.0,
            "timestamp": datetime.now().isoformat(),
        }

        # Mock iron condor analysis results
        mock_iron_condor_df = pd.DataFrame(
            {
                "symbol": ["AAPL"],
                "strategy_type": ["iron_condor"],
                "short_put_strike": [145.0],
                "long_put_strike": [140.0],
                "short_call_strike": [160.0],
                "long_call_strike": [165.0],
                "expiration": ["2024-02-16"],
                "dte": [30],
                "max_profit": [200],
                "max_loss": [300],
                "probability_of_profit": [0.75],
                "annual_roi": [0.25],
            }
        )
        mock_iron_condor.return_value = mock_iron_condor_df

        analysis_data = {"account_id": sample_account, "strategy_type": "iron_condors", "symbols": ["AAPL"]}

        response = client.post(
            "/api/strategies/options/analyze", data=json.dumps(analysis_data), content_type="application/json"
        )

        assert response.status_code == 200
        data = json.loads(response.data)
        assert "candidates" in data
        assert "market_conditions" in data
        assert data["status"] == "success"


class TestDatabaseSchema:
    """Test the database schema for options analysis."""

    def test_options_tables_exist(self, test_db):
        """Test that all required options tables exist."""
        conn = get_db_connection()

        required_tables = ["user_watchlists", "strategy_configs"]

        for table in required_tables:
            result = conn.execute(
                """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name=?
            """,
                (table,),
            ).fetchone()

            assert result is not None, f"Table {table} does not exist"

        conn.close()

    def test_watchlist_table_structure(self, test_db):
        """Test the watchlist table structure."""
        conn = get_db_connection()

        # Test table structure
        columns = conn.execute("PRAGMA table_info(user_watchlists)").fetchall()
        column_names = [col[1] for col in columns]

        expected_columns = ["watchlist_id", "account_id", "name", "symbols", "created_at", "updated_at"]

        for col in expected_columns:
            assert col in column_names, f"Column {col} missing from user_watchlists"

        conn.close()

    def test_strategy_configs_table_structure(self, test_db):
        """Test the strategy configs table structure."""
        conn = get_db_connection()

        columns = conn.execute("PRAGMA table_info(strategy_configs)").fetchall()
        column_names = [col[1] for col in columns]

        expected_columns = [
            "config_id",
            "account_id",
            "strategy_type",
            "config_data",
            "is_default",
            "created_at",
            "updated_at",
        ]

        for col in expected_columns:
            assert col in column_names, f"Column {col} missing from strategy_configs"

        conn.close()


if __name__ == "__main__":
    pytest.main([__file__])
