"""
Pytest configuration and shared fixtures for the stock trading application.
"""

import os
import shutil
import sqlite3

# Import the application
import sys
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch

import numpy as np
import pandas as pd
import pytest
from flask import Flask

sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "src"))

from src.api import get_db_connection
from src.app import app as flask_app
from src.utils.logger import get_logger
from src.utils.stock_cache import StockCache


@pytest.fixture(scope="session")
def test_db_path():
    """Create a temporary database for testing."""
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test_stock_trading.db")

    # Create the test database with schema
    schema_path = os.path.join(os.path.dirname(__file__), "..", "config", "schema.sql")
    if os.path.exists(schema_path):
        with open(schema_path, "r") as f:
            schema_sql = f.read()

        conn = sqlite3.connect(db_path)
        conn.executescript(schema_sql)
        conn.close()

    yield db_path

    # Cleanup
    shutil.rmtree(temp_dir, ignore_errors=True)


@pytest.fixture
def test_db(test_db_path, monkeypatch):
    """Provide a clean database for each test."""
    # Copy the schema database to a new test database
    test_instance_path = test_db_path.replace(".db", f"_{datetime.now().timestamp()}.db")
    shutil.copy2(test_db_path, test_instance_path)

    # Mock the get_db_connection function to use our test database
    def mock_get_db_connection():
        conn = sqlite3.connect(test_instance_path)
        conn.row_factory = sqlite3.Row
        return conn

    # Patch all possible import paths for get_db_connection
    monkeypatch.setattr("src.api.get_db_connection", mock_get_db_connection)

    # Import the module and patch the function directly
    import src.api

    monkeypatch.setattr(src.api, "get_db_connection", mock_get_db_connection)

    # Also patch individual API modules that import get_db_connection
    try:
        import src.api.accounts

        monkeypatch.setattr(src.api.accounts, "get_db_connection", mock_get_db_connection)
    except ImportError:
        pass

    try:
        import src.api.transactions

        monkeypatch.setattr(src.api.transactions, "get_db_connection", mock_get_db_connection)
    except ImportError:
        pass

    try:
        import src.api.analytics

        monkeypatch.setattr(src.api.analytics, "get_db_connection", mock_get_db_connection)
    except ImportError:
        pass

    try:
        import src.api.market

        monkeypatch.setattr(src.api.market, "get_db_connection", mock_get_db_connection)
    except ImportError:
        pass

    try:
        import src.api.strategies

        monkeypatch.setattr(src.api.strategies, "get_db_connection", mock_get_db_connection)
    except ImportError:
        pass

    try:
        import src.services.options_service

        monkeypatch.setattr(src.services.options_service, "get_db_connection", mock_get_db_connection)
        # Also patch the function directly in the module's globals
        src.services.options_service.get_db_connection = mock_get_db_connection
    except ImportError:
        pass

    # Clear any existing data in the test database
    conn = mock_get_db_connection()
    cursor = conn.cursor()

    # Clear all tables that might have test data
    tables_to_clear = [
        "user_watchlists",
        "strategy_configs",
        "accounts",
        "transactions",
        "cached_prices",
        "daily_returns",
        "portfolio_snapshots",
        "realized_gains",
    ]

    for table in tables_to_clear:
        try:
            cursor.execute(f"DELETE FROM {table}")
        except sqlite3.OperationalError:
            # Table might not exist, skip
            pass

    conn.commit()
    conn.close()

    yield test_instance_path

    # Cleanup
    if os.path.exists(test_instance_path):
        os.remove(test_instance_path)


@pytest.fixture
def app(test_db, monkeypatch):
    """Create a Flask app configured for testing."""
    # Disable scheduler during tests
    monkeypatch.setattr("src.app.scheduler_initialized", True)
    monkeypatch.setattr("src.core.scheduler.start_scheduler", lambda: None)
    monkeypatch.setattr("src.core.scheduler.stop_scheduler", lambda: None)

    flask_app.config.update({"TESTING": True, "WTF_CSRF_ENABLED": False, "DATABASE_PATH": test_db})

    with flask_app.app_context():
        yield flask_app


@pytest.fixture
def client(app):
    """Create a test client for the Flask app."""
    return app.test_client()


@pytest.fixture
def mock_stock_cache():
    """Mock the StockCache for testing."""
    with patch("src.utils.stock_cache.StockCache") as mock_cache:
        cache_instance = Mock()
        mock_cache.return_value = cache_instance

        # Mock common cache methods
        cache_instance.get_price.return_value = 150.0
        cache_instance.get_prices.return_value = {"AAPL": 150.0, "GOOGL": 2500.0}
        cache_instance.update_price.return_value = True
        cache_instance.is_cache_valid.return_value = True

        yield cache_instance


@pytest.fixture
def mock_yfinance():
    """Mock yfinance for testing."""
    with patch("yfinance.Ticker") as mock_ticker:
        ticker_instance = Mock()
        mock_ticker.return_value = ticker_instance

        # Mock ticker data
        ticker_instance.info = {
            "symbol": "AAPL",
            "longName": "Apple Inc.",
            "currentPrice": 150.0,
            "marketCap": 2500000000000,
            "sector": "Technology",
        }

        # Mock history data
        dates = pd.date_range(start="2023-01-01", end="2023-12-31", freq="D")
        ticker_instance.history.return_value = pd.DataFrame(
            {
                "Open": np.random.uniform(140, 160, len(dates)),
                "High": np.random.uniform(145, 165, len(dates)),
                "Low": np.random.uniform(135, 155, len(dates)),
                "Close": np.random.uniform(140, 160, len(dates)),
                "Volume": np.random.randint(1000000, ********, len(dates)),
            },
            index=dates,
        )

        yield ticker_instance


@pytest.fixture
def sample_account_data():
    """Sample account data for testing."""
    return {
        "account_id": "TEST001",
        "account_name": "Test Account",
        "account_type": "Individual",
        "created_at": datetime.now().isoformat(),
    }


@pytest.fixture
def sample_transaction_data():
    """Sample transaction data for testing."""
    return {"account_id": 1, "symbol": "AAPL", "quantity": 100, "price": 150.0, "trans_time": datetime.now().isoformat()}


@pytest.fixture
def sample_portfolio_data():
    """Sample portfolio data for testing."""
    return [
        {
            "account_id": 1,
            "symbol": "AAPL",
            "quantity": 100,
            "avg_cost": 145.0,
            "current_price": 150.0,
            "market_value": 15000.0,
            "unrealized_gain": 500.0,
            "unrealized_gain_pct": 3.45,
        },
        {
            "account_id": 1,
            "symbol": "GOOGL",
            "quantity": 10,
            "avg_cost": 2400.0,
            "current_price": 2500.0,
            "market_value": 25000.0,
            "unrealized_gain": 1000.0,
            "unrealized_gain_pct": 4.17,
        },
    ]


@pytest.fixture
def mock_llm_service():
    """Mock the LLM service for testing."""
    with patch("src.utils.llm.LLMService") as mock_llm:
        llm_instance = Mock()
        mock_llm.return_value = llm_instance

        # Mock LLM responses
        llm_instance.call_llm_api.return_value = '{"analysis": "Test analysis", "recommendation": "HOLD"}'
        llm_instance.analyze_portfolio.return_value = {
            "overall_sentiment": "POSITIVE",
            "risk_level": "MEDIUM",
            "recommendations": ["Hold current positions", "Consider diversification"],
        }

        yield llm_instance


@pytest.fixture
def mock_scheduler():
    """Mock the scheduler for testing."""
    with patch("src.core.scheduler.start_scheduler") as mock_start, patch("src.core.scheduler.stop_scheduler") as mock_stop:
        yield {"start": mock_start, "stop": mock_stop}


@pytest.fixture(autouse=True)
def disable_logging():
    """Disable logging during tests to reduce noise."""
    import logging

    logging.disable(logging.CRITICAL)
    yield
    logging.disable(logging.NOTSET)


def populate_test_data(db_path, account_data, transaction_data):
    """Helper function to populate test database with sample data."""
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # Insert account
    cursor.execute(
        """
        INSERT INTO accounts (name, created_at)
        VALUES (?, ?)
    """,
        (account_data["account_name"], account_data["created_at"]),
    )
    account_id = cursor.lastrowid

    # Insert transaction
    cursor.execute(
        """
        INSERT INTO transactions (account_id, symbol, quantity, price, trans_time)
        VALUES (?, ?, ?, ?, ?)
    """,
        (
            account_id,
            transaction_data["symbol"],
            transaction_data["quantity"],
            transaction_data["price"],
            transaction_data["trans_time"],
        ),
    )

    conn.commit()
    conn.close()


# Options-specific fixtures


@pytest.fixture
def sample_options_data():
    """Create sample options data for basic testing."""
    base_date = datetime.now() + timedelta(days=30)
    expiration = base_date.strftime("%Y-%m-%d")

    return pd.DataFrame(
        [
            {
                "symbol": "AAPL",
                "optionType": "puts",
                "strike": 145.0,
                "expiration": expiration,
                "dte": 30,
                "bid": 3.0,
                "ask": 3.5,
                "impliedVolatility": 0.28,
                "volume": 80,
                "openInterest": 400,
            },
            {
                "symbol": "MSFT",
                "optionType": "calls",
                "strike": 310.0,
                "expiration": expiration,
                "dte": 30,
                "bid": 8.0,
                "ask": 8.5,
                "impliedVolatility": 0.25,
                "volume": 120,
                "openInterest": 600,
            },
        ]
    )


@pytest.fixture
def comprehensive_options_data():
    """Create comprehensive options data for testing all strategies."""
    base_date = datetime.now() + timedelta(days=30)
    expiration = base_date.strftime("%Y-%m-%d")

    data = []

    # AAPL options (current price: 150.0)
    aapl_strikes_puts = [130, 135, 140, 145, 148, 150]
    aapl_strikes_calls = [150, 152, 155, 160, 165, 170, 175, 180]

    for strike in aapl_strikes_puts:
        data.append(
            {
                "symbol": "AAPL",
                "optionType": "puts",
                "strike": strike,
                "expiration": expiration,
                "dte": 30,
                "bid": max(0.1, (150 - strike) * 0.6 + np.random.uniform(0.5, 2.0)),
                "ask": max(0.2, (150 - strike) * 0.6 + np.random.uniform(1.0, 2.5)),
                "impliedVolatility": 0.20 + np.random.uniform(0.05, 0.15),
                "volume": np.random.randint(50, 500),
                "openInterest": np.random.randint(100, 1000),
            }
        )

    for strike in aapl_strikes_calls:
        data.append(
            {
                "symbol": "AAPL",
                "optionType": "calls",
                "strike": strike,
                "expiration": expiration,
                "dte": 30,
                "bid": max(0.1, max(0, 150 - strike) + np.random.uniform(0.5, 3.0)),
                "ask": max(0.2, max(0, 150 - strike) + np.random.uniform(1.0, 3.5)),
                "impliedVolatility": 0.18 + np.random.uniform(0.05, 0.12),
                "volume": np.random.randint(30, 400),
                "openInterest": np.random.randint(80, 800),
            }
        )

    return pd.DataFrame(data)


@pytest.fixture
def sample_options_prices():
    """Create sample current stock prices for options testing."""
    return {"AAPL": 150.0, "MSFT": 300.0, "GOOGL": 2500.0, "AMZN": 3200.0, "TSLA": 800.0}


@pytest.fixture
def default_put_params():
    """Default parameters for cash-secured puts testing."""
    return {
        "min_dte": 20,
        "max_dte": 60,
        "min_annual_roi": 0.10,
        "min_buffer_percent": 0.02,
        "max_delta": 0.35,
        "sort_by": "winRateScore",
        "max_candidates": 10,
    }


@pytest.fixture
def default_call_params():
    """Default parameters for covered calls testing."""
    return {
        "min_dte": 20,
        "max_dte": 60,
        "min_annual_roi": 0.08,
        "min_upside_buffer": 0.01,
        "max_delta": 0.35,
        "sort_by": "annualizedRoi",
        "max_candidates": 10,
    }


@pytest.fixture
def sample_market_conditions():
    """Create sample market conditions data."""
    return {"volatility_regime": "Normal", "stress_indicator": 45.0, "timestamp": datetime.now().isoformat()}


@pytest.fixture
def strategy_configs():
    """Create sample strategy configurations for testing."""
    return {
        "cash_secured_puts": {
            "min_dte": 20,
            "max_dte": 60,
            "min_annual_roi": 0.15,
            "max_delta": 0.30,
            "min_buffer_percent": 0.05,
            "sort_by": "winRateScore",
            "max_candidates": 10,
        },
        "covered_calls": {
            "min_dte": 20,
            "max_dte": 60,
            "min_annual_roi": 0.10,
            "max_delta": 0.30,
            "min_upside_buffer": 0.02,
            "sort_by": "annualizedRoi",
            "max_candidates": 10,
        },
        "iron_condors": {
            "min_dte": 30,
            "max_dte": 60,
            "min_annual_roi": 0.20,
            "target_delta_short_put": 0.20,
            "target_delta_short_call": 0.20,
            "min_wing_width": 5,
            "max_wing_width": 20,
            "sort_by": "winRateScore",
            "max_candidates": 5,
        },
    }


@pytest.fixture
def mock_options_data_manager():
    """Mock the OptionsDataManager for testing."""
    with patch("services.options_data.OptionsDataManager") as mock_manager:
        manager_instance = Mock()
        mock_manager.return_value = manager_instance

        # Mock common methods
        manager_instance.fetch_data_for_batch.return_value = (pd.DataFrame(), {})
        manager_instance.get_market_conditions.return_value = {
            "volatility_regime": "Normal",
            "stress_indicator": 45.0,
            "timestamp": datetime.now().isoformat(),
        }
        manager_instance.detect_volatility_regime.return_value = "Normal"
        manager_instance.calculate_market_stress_indicator.return_value = 45.0

        yield manager_instance
